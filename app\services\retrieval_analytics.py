"""
Retrieval Quality Analytics Service

This module provides comprehensive analytics for the RAG system, including:
- Query success rate analysis
- Document relevance scoring
- System performance bottlenecks
- User satisfaction metrics
- Retrieval quality trends
"""

import json
import time
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Tuple, Any
from collections import defaultdict, deque
import threading
from enum import Enum

import numpy as np
from sklearn.metrics import precision_score, recall_score, f1_score
import logging

logger = logging.getLogger(__name__)


class QueryOutcome(Enum):
    """Query outcome classification"""
    SUCCESS = "success"
    PARTIAL_SUCCESS = "partial_success"
    FAILURE = "failure"
    TIMEOUT = "timeout"
    ERROR = "error"


class RelevanceLevel(Enum):
    """Document relevance levels"""
    HIGHLY_RELEVANT = "highly_relevant"
    RELEVANT = "relevant"
    MODERATELY_RELEVANT = "moderately_relevant"
    LOW_RELEVANCE = "low_relevance"
    IRRELEVANT = "irrelevant"


@dataclass
class QueryAnalytics:
    """Analytics data for a single query"""
    query_id: str
    timestamp: datetime
    query_text: str
    category: str
    outcome: QueryOutcome
    execution_time: float
    documents_retrieved: int
    documents_returned: int
    cache_hit: bool
    relevance_scores: List[float]
    user_feedback: Optional[int] = None  # 1-5 scale
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None


@dataclass
class SystemPerformanceMetrics:
    """System-wide performance metrics"""
    timestamp: datetime
    total_queries: int
    successful_queries: int
    failed_queries: int
    average_execution_time: float
    cache_hit_rate: float
    average_relevance_score: float
    memory_usage_mb: float
    cpu_usage_percent: float
    active_connections: int


@dataclass
class RetrievalQualityMetrics:
    """Retrieval quality analysis"""
    precision_at_k: float
    recall_at_k: float
    f1_score_at_k: float
    mean_reciprocal_rank: float
    normalized_discounted_cumulative_gain: float
    average_relevance_score: float
    diversity_score: float
    coverage_score: float


class RetrievalAnalytics:
    """Advanced analytics service for RAG system performance"""
    
    def __init__(self, max_history_size: int = 10000):
        self.max_history_size = max_history_size
        self.query_history: deque = deque(maxlen=max_history_size)
        self.performance_history: deque = deque(maxlen=max_history_size)
        self.category_analytics: Dict[str, Dict] = defaultdict(lambda: {
            'total_queries': 0,
            'successful_queries': 0,
            'failed_queries': 0,
            'average_execution_time': 0.0,
            'average_relevance_score': 0.0,
            'cache_hit_rate': 0.0,
            'user_satisfaction': 0.0
        })
        self.lock = threading.RLock()
        
        # Performance tracking
        self.start_time = datetime.now()
        self.total_queries = 0
        self.successful_queries = 0
        self.failed_queries = 0
        self.total_execution_time = 0.0
        self.total_cache_hits = 0
        self.total_relevance_scores = []
        
        logger.info("Retrieval analytics service initialized")
    
    def record_query(self, query_analytics: QueryAnalytics) -> None:
        """Record analytics for a single query"""
        with self.lock:
            self.query_history.append(query_analytics)
            self.total_queries += 1
            self.total_execution_time += query_analytics.execution_time
            
            if query_analytics.outcome == QueryOutcome.SUCCESS:
                self.successful_queries += 1
            else:
                self.failed_queries += 1
            
            if query_analytics.cache_hit:
                self.total_cache_hits += 1
            
            if query_analytics.relevance_scores:
                self.total_relevance_scores.extend(query_analytics.relevance_scores)
            
            # Update category analytics
            category = query_analytics.category
            self.category_analytics[category]['total_queries'] += 1
            
            if query_analytics.outcome == QueryOutcome.SUCCESS:
                self.category_analytics[category]['successful_queries'] += 1
            else:
                self.category_analytics[category]['failed_queries'] += 1
            
            # Update running averages
            self._update_category_averages(category, query_analytics)
            
            logger.debug(f"Recorded analytics for query {query_analytics.query_id}")
    
    def _update_category_averages(self, category: str, query_analytics: QueryAnalytics) -> None:
        """Update running averages for a category"""
        cat_data = self.category_analytics[category]
        total = cat_data['total_queries']
        
        # Update execution time average
        current_avg = cat_data['average_execution_time']
        cat_data['average_execution_time'] = (
            (current_avg * (total - 1) + query_analytics.execution_time) / total
        )
        
        # Update relevance score average
        if query_analytics.relevance_scores:
            avg_relevance = np.mean(query_analytics.relevance_scores)
            current_relevance_avg = cat_data['average_relevance_score']
            cat_data['average_relevance_score'] = (
                (current_relevance_avg * (total - 1) + avg_relevance) / total
            )
        
        # Update cache hit rate
        if query_analytics.cache_hit:
            cat_data['cache_hit_rate'] = (
                (cat_data['cache_hit_rate'] * (total - 1) + 1) / total
            )
        else:
            cat_data['cache_hit_rate'] = (
                (cat_data['cache_hit_rate'] * (total - 1)) / total
            )
        
        # Update user satisfaction
        if query_analytics.user_feedback:
            current_satisfaction = cat_data['user_satisfaction']
            cat_data['user_satisfaction'] = (
                (current_satisfaction * (total - 1) + query_analytics.user_feedback) / total
            )
    
    def record_performance_metrics(self, metrics: SystemPerformanceMetrics) -> None:
        """Record system performance metrics"""
        with self.lock:
            self.performance_history.append(metrics)
            logger.debug(f"Recorded performance metrics at {metrics.timestamp}")
    
    def get_system_analytics(self, time_window_hours: int = 24) -> Dict[str, Any]:
        """Get comprehensive system analytics for the specified time window"""
        with self.lock:
            cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
            
            # Filter queries within time window
            recent_queries = [
                q for q in self.query_history 
                if q.timestamp >= cutoff_time
            ]
            
            if not recent_queries:
                return self._get_empty_analytics()
            
            # Calculate metrics
            total_queries = len(recent_queries)
            successful_queries = len([q for q in recent_queries if q.outcome == QueryOutcome.SUCCESS])
            failed_queries = total_queries - successful_queries
            
            execution_times = [q.execution_time for q in recent_queries]
            cache_hits = [q.cache_hit for q in recent_queries]
            relevance_scores = [score for q in recent_queries for score in q.relevance_scores]
            
            # Calculate averages
            avg_execution_time = np.mean(execution_times) if execution_times else 0.0
            cache_hit_rate = np.mean(cache_hits) if cache_hits else 0.0
            avg_relevance_score = np.mean(relevance_scores) if relevance_scores else 0.0
            
            # Calculate success rate by category
            category_success_rates = {}
            for category in set(q.category for q in recent_queries):
                category_queries = [q for q in recent_queries if q.category == category]
                category_successful = len([q for q in category_queries if q.outcome == QueryOutcome.SUCCESS])
                category_success_rates[category] = category_successful / len(category_queries)
            
            # Calculate performance trends
            performance_trends = self._calculate_performance_trends(time_window_hours)
            
            return {
                'time_window_hours': time_window_hours,
                'total_queries': total_queries,
                'successful_queries': successful_queries,
                'failed_queries': failed_queries,
                'success_rate': successful_queries / total_queries if total_queries > 0 else 0.0,
                'average_execution_time': avg_execution_time,
                'cache_hit_rate': cache_hit_rate,
                'average_relevance_score': avg_relevance_score,
                'category_success_rates': category_success_rates,
                'performance_trends': performance_trends,
                'system_uptime_hours': (datetime.now() - self.start_time).total_seconds() / 3600,
                'category_analytics': dict(self.category_analytics)
            }
    
    def _calculate_performance_trends(self, time_window_hours: int) -> Dict[str, Any]:
        """Calculate performance trends over time"""
        cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
        
        # Group queries by hour
        hourly_data = defaultdict(lambda: {
            'queries': 0,
            'successful': 0,
            'execution_times': [],
            'relevance_scores': []
        })
        
        for query in self.query_history:
            if query.timestamp >= cutoff_time:
                hour_key = query.timestamp.replace(minute=0, second=0, microsecond=0)
                hourly_data[hour_key]['queries'] += 1
                
                if query.outcome == QueryOutcome.SUCCESS:
                    hourly_data[hour_key]['successful'] += 1
                
                hourly_data[hour_key]['execution_times'].append(query.execution_time)
                hourly_data[hour_key]['relevance_scores'].extend(query.relevance_scores)
        
        # Calculate trends
        hours = sorted(hourly_data.keys())
        if len(hours) < 2:
            return {'trend_available': False}
        
        # Success rate trend
        success_rates = [
            hourly_data[hour]['successful'] / hourly_data[hour]['queries']
            for hour in hours
            if hourly_data[hour]['queries'] > 0
        ]
        
        # Execution time trend
        avg_execution_times = [
            np.mean(hourly_data[hour]['execution_times'])
            for hour in hours
            if hourly_data[hour]['execution_times']
        ]
        
        # Relevance score trend
        avg_relevance_scores = [
            np.mean(hourly_data[hour]['relevance_scores'])
            for hour in hours
            if hourly_data[hour]['relevance_scores']
        ]
        
        return {
            'trend_available': True,
            'success_rate_trend': self._calculate_trend(success_rates),
            'execution_time_trend': self._calculate_trend(avg_execution_times),
            'relevance_score_trend': self._calculate_trend(avg_relevance_scores),
            'data_points': len(hours)
        }
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction from a list of values"""
        if len(values) < 2:
            return 'insufficient_data'
        
        # Simple linear trend calculation
        x = np.arange(len(values))
        slope = np.polyfit(x, values, 1)[0]
        
        if slope > 0.01:
            return 'improving'
        elif slope < -0.01:
            return 'declining'
        else:
            return 'stable'
    
    def _get_empty_analytics(self) -> Dict[str, Any]:
        """Return empty analytics structure"""
        return {
            'time_window_hours': 24,
            'total_queries': 0,
            'successful_queries': 0,
            'failed_queries': 0,
            'success_rate': 0.0,
            'average_execution_time': 0.0,
            'cache_hit_rate': 0.0,
            'average_relevance_score': 0.0,
            'category_success_rates': {},
            'performance_trends': {'trend_available': False},
            'system_uptime_hours': (datetime.now() - self.start_time).total_seconds() / 3600,
            'category_analytics': dict(self.category_analytics)
        }
    
    def get_retrieval_quality_metrics(self, query_id: str) -> Optional[RetrievalQualityMetrics]:
        """Calculate retrieval quality metrics for a specific query"""
        with self.lock:
            query_analytics = next(
                (q for q in self.query_history if q.query_id == query_id), 
                None
            )
            
            if not query_analytics or not query_analytics.relevance_scores:
                return None
            
            relevance_scores = query_analytics.relevance_scores
            k = len(relevance_scores)
            
            # Calculate precision, recall, and F1 score
            # For this implementation, we'll use relevance scores as binary relevance
            # (scores > 0.5 are considered relevant)
            binary_relevance = [1 if score > 0.5 else 0 for score in relevance_scores]
            
            if sum(binary_relevance) == 0:
                return None
            
            # Calculate metrics
            precision = precision_score([1] * k, binary_relevance, zero_division=0)
            recall = recall_score([1] * k, binary_relevance, zero_division=0)
            f1 = f1_score([1] * k, binary_relevance, zero_division=0)
            
            # Calculate Mean Reciprocal Rank (MRR)
            mrr = 0.0
            for i, score in enumerate(relevance_scores):
                if score > 0.5:  # Consider relevant
                    mrr += 1.0 / (i + 1)
                    break
            
            # Calculate Normalized Discounted Cumulative Gain (NDCG)
            dcg = sum(score / np.log2(i + 2) for i, score in enumerate(relevance_scores))
            idcg = sum(1.0 / np.log2(i + 2) for i in range(min(k, sum(binary_relevance))))
            ndcg = dcg / idcg if idcg > 0 else 0.0
            
            # Calculate diversity score (based on score variance)
            diversity_score = np.var(relevance_scores) if len(relevance_scores) > 1 else 0.0
            
            # Calculate coverage score (percentage of relevant documents)
            coverage_score = sum(binary_relevance) / k
            
            return RetrievalQualityMetrics(
                precision_at_k=precision,
                recall_at_k=recall,
                f1_score_at_k=f1,
                mean_reciprocal_rank=mrr,
                normalized_discounted_cumulative_gain=ndcg,
                average_relevance_score=np.mean(relevance_scores),
                diversity_score=diversity_score,
                coverage_score=coverage_score
            )
    
    def get_bottleneck_analysis(self) -> Dict[str, Any]:
        """Analyze system bottlenecks and performance issues"""
        with self.lock:
            if not self.query_history:
                return {'analysis_available': False}
            
            # Analyze execution time distribution
            execution_times = [q.execution_time for q in self.query_history]
            avg_execution_time = np.mean(execution_times)
            std_execution_time = np.std(execution_times)
            
            # Identify slow queries (execution time > 2 standard deviations above mean)
            slow_threshold = avg_execution_time + 2 * std_execution_time
            slow_queries = [q for q in self.query_history if q.execution_time > slow_threshold]
            
            # Analyze failure patterns
            failed_queries = [q for q in self.query_history if q.outcome != QueryOutcome.SUCCESS]
            failure_reasons = defaultdict(int)
            for query in failed_queries:
                failure_reasons[query.outcome.value] += 1
            
            # Analyze cache performance
            cache_hit_rate = self.total_cache_hits / self.total_queries if self.total_queries > 0 else 0.0
            
            # Analyze category performance
            category_performance = {}
            for category, data in self.category_analytics.items():
                if data['total_queries'] > 0:
                    category_performance[category] = {
                        'success_rate': data['successful_queries'] / data['total_queries'],
                        'avg_execution_time': data['average_execution_time'],
                        'cache_hit_rate': data['cache_hit_rate'],
                        'avg_relevance_score': data['average_relevance_score']
                    }
            
            return {
                'analysis_available': True,
                'execution_time_analysis': {
                    'average': avg_execution_time,
                    'standard_deviation': std_execution_time,
                    'slow_threshold': slow_threshold,
                    'slow_queries_count': len(slow_queries),
                    'slow_queries_percentage': len(slow_queries) / len(self.query_history) * 100
                },
                'failure_analysis': {
                    'total_failures': len(failed_queries),
                    'failure_rate': len(failed_queries) / len(self.query_history),
                    'failure_reasons': dict(failure_reasons)
                },
                'cache_analysis': {
                    'overall_hit_rate': cache_hit_rate,
                    'recommendation': 'improve_cache_strategy' if cache_hit_rate < 0.3 else 'cache_performing_well'
                },
                'category_performance': category_performance,
                'recommendations': self._generate_recommendations(
                    avg_execution_time, cache_hit_rate, len(slow_queries), failure_reasons
                )
            }
    
    def _generate_recommendations(self, avg_execution_time: float, cache_hit_rate: float, 
                                slow_queries_count: int, failure_reasons: Dict[str, int]) -> List[str]:
        """Generate performance improvement recommendations"""
        recommendations = []
        
        if avg_execution_time > 2.0:
            recommendations.append("Consider optimizing query processing or increasing computational resources")
        
        if cache_hit_rate < 0.3:
            recommendations.append("Implement more aggressive caching strategies")
        
        if slow_queries_count > len(self.query_history) * 0.1:  # More than 10% slow queries
            recommendations.append("Investigate and optimize slow query patterns")
        
        if failure_reasons.get('timeout', 0) > 0:
            recommendations.append("Increase timeout limits or optimize query complexity")
        
        if failure_reasons.get('error', 0) > 0:
            recommendations.append("Review error logs and implement better error handling")
        
        if not recommendations:
            recommendations.append("System performance is within acceptable parameters")
        
        return recommendations
    
    def export_analytics(self, format: str = 'json') -> str:
        """Export analytics data in specified format"""
        with self.lock:
            data = {
                'system_analytics': self.get_system_analytics(),
                'bottleneck_analysis': self.get_bottleneck_analysis(),
                'category_analytics': dict(self.category_analytics),
                'export_timestamp': datetime.now().isoformat()
            }
            
            if format.lower() == 'json':
                return json.dumps(data, indent=2, default=str)
            else:
                raise ValueError(f"Unsupported export format: {format}")


# Global analytics instance
retrieval_analytics = RetrievalAnalytics()


def get_retrieval_analytics() -> RetrievalAnalytics:
    """Get the global retrieval analytics instance"""
    return retrieval_analytics


def record_query_analytics(query_id: str, query_text: str, category: str, 
                          outcome: QueryOutcome, execution_time: float,
                          documents_retrieved: int, documents_returned: int,
                          cache_hit: bool, relevance_scores: List[float],
                          user_feedback: Optional[int] = None,
                          error_message: Optional[str] = None,
                          metadata: Optional[Dict[str, Any]] = None) -> None:
    """Convenience function to record query analytics"""
    analytics = QueryAnalytics(
        query_id=query_id,
        timestamp=datetime.now(),
        query_text=query_text,
        category=category,
        outcome=outcome,
        execution_time=execution_time,
        documents_retrieved=documents_retrieved,
        documents_returned=documents_returned,
        cache_hit=cache_hit,
        relevance_scores=relevance_scores,
        user_feedback=user_feedback,
        error_message=error_message,
        metadata=metadata or {}
    )
    
    retrieval_analytics.record_query(analytics) 