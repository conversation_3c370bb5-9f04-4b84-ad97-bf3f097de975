"""
Cross-Category Retrieval Service

This module provides intelligent cross-category document retrieval capabilities,
combining results from multiple categories with dynamic weighting and relevance
scoring.
"""

import logging
import time
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass, field
from datetime import datetime
import numpy as np
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from langchain_core.documents import Document
from langchain_core.vectorstores import VectorStoreRetriever

from app.services.category_router import route_query_to_categories, CategoryWeight, CategoryRoutingResult
from app.services.unified_vector_db import get_unified_vector_db
from app.services.enhanced_relevance_scorer import score_documents_enhanced_batch, ScoredDocument
from app.services.hybrid_search_service import hybrid_search
from app.services.multi_stage_retriever import retrieve_documents_multi_stage
from app.utils.rag_performance import get_rag_monitor, track_hybrid_search_metrics
from app.services.cache_service import cache_service

logger = logging.getLogger(__name__)

@dataclass
class CategoryResultSection:
    """Results from a specific category with metadata"""
    category: str
    documents: List[Document]
    is_primary: bool
    weight: float
    document_count: int

@dataclass
class CrossCategoryResult:
    """Result of cross-category retrieval with two-tier ranking"""
    # Two-tier structure: primary categories first, then secondary
    primary_results: List[CategoryResultSection]
    secondary_results: List[CategoryResultSection]
    all_documents: List[Document]  # Flattened list for backward compatibility
    category_weights: List[CategoryWeight]
    routing_result: CategoryRoutingResult
    total_documents_retrieved: int
    documents_per_category: Dict[str, int]
    execution_time: float
    cache_hit_rate: float
    metadata: Dict[str, Any] = field(default_factory=dict)

class CrossCategoryRetriever:
    """
    Intelligent cross-category document retrieval system that combines
    results from multiple categories with dynamic weighting.
    """
    
    def __init__(self):
        self.vector_db = None  # Will be initialized per category
        self.max_workers = 3  # Limit concurrent category searches
        self.min_documents_per_category = 2
        self.max_documents_per_category = 15
        
    def retrieve_cross_category(self, query: str, user_context: Optional[Dict[str, Any]] = None,
                              max_total_documents: int = 30) -> CrossCategoryResult:
        """
        Retrieve documents from multiple categories using intelligent routing.
        
        Args:
            query: The user query
            user_context: Optional user context for personalized routing
            max_total_documents: Maximum total documents to return
            
        Returns:
            CrossCategoryResult with documents and metadata
        """
        start_time = datetime.now()
        
        # Check cache first
        cache_key = f"cross_category:{hash(query)}"
        cached_result = cache_service.get(cache_key)
        if cached_result:
            logger.info("Using cached cross-category result")
            return CrossCategoryResult(**cached_result)
        
        try:
            # Route query to categories
            routing_result = route_query_to_categories(query, user_context)
            
            # Determine retrieval strategy based on routing
            if routing_result.routing_strategy == 'comprehensive_search':
                result = self._comprehensive_search(query, routing_result, max_total_documents)
            elif routing_result.routing_strategy == 'balanced_search':
                result = self._balanced_search(query, routing_result, max_total_documents)
            else:
                result = self._focused_search(query, routing_result, max_total_documents)
            
            # Calculate cache hit rate
            cache_hit_rate = self._calculate_cache_hit_rate(query, routing_result)
            
            # Add metadata
            result.metadata.update({
                'routing_strategy': routing_result.routing_strategy,
                'cross_category_threshold': routing_result.cross_category_threshold,
                'cache_hit': False
            })
            
            # Cache the result
            cache_service.set(cache_key, result.__dict__, ttl=1800)  # 30 minutes
            
            logger.info(f"Cross-category retrieval completed in {result.execution_time:.3f}s")
            logger.info(f"Retrieved {result.total_documents_retrieved} documents from {len(result.documents_per_category)} categories")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in cross-category retrieval: {e}")
            return self._create_fallback_result(query, start_time)
    
    def _comprehensive_search(self, query: str, routing_result: CategoryRoutingResult,
                            max_total_documents: int) -> CrossCategoryResult:
        """Comprehensive search across all relevant categories with two-tier ranking"""

        start_time = datetime.now()
        all_documents = []
        documents_per_category = {}

        # Get all categories (primary + secondary)
        all_categories = routing_result.primary_categories + routing_result.secondary_categories

        # Calculate documents per category based on weights
        category_allocations = self._calculate_category_allocations(
            all_categories, max_total_documents
        )

        # Retrieve documents from each category in parallel
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_category = {}

            for category_weight in all_categories:
                category = category_weight.category
                allocation = category_allocations.get(category, self.min_documents_per_category)

                future = executor.submit(
                    self._retrieve_from_category,
                    query, category, allocation, routing_result.query_analysis
                )
                future_to_category[future] = category

            # Collect results
            for future in as_completed(future_to_category):
                category = future_to_category[future]
                try:
                    category_docs = future.result()
                    # Add category metadata to each document
                    for doc in category_docs:
                        doc.metadata['source_category'] = category
                        doc.metadata['is_cross_category'] = True
                    all_documents.extend(category_docs)
                    documents_per_category[category] = len(category_docs)
                except Exception as e:
                    logger.error(f"Error retrieving from category {category}: {e}")
                    documents_per_category[category] = 0

        # Score and rank all documents
        scored_docs = score_documents_enhanced_batch(all_documents, query)

        # Apply cross-category threshold filtering
        filtered_docs = self._apply_cross_category_filter(
            scored_docs, routing_result.cross_category_threshold
        )

        # Create two-tier result structure
        primary_results, secondary_results = self._create_two_tier_results(
            filtered_docs, routing_result, max_total_documents
        )

        # Create flattened list for backward compatibility
        all_docs_flattened = []
        for section in primary_results + secondary_results:
            all_docs_flattened.extend(section.documents)

        execution_time = (datetime.now() - start_time).total_seconds()

        return CrossCategoryResult(
            primary_results=primary_results,
            secondary_results=secondary_results,
            all_documents=all_docs_flattened,
            category_weights=all_categories,
            routing_result=routing_result,
            total_documents_retrieved=len(all_docs_flattened),
            documents_per_category=documents_per_category,
            execution_time=execution_time,
            cache_hit_rate=0.0,
            metadata={'search_type': 'comprehensive'}
        )
    
    def _balanced_search(self, query: str, routing_result: CategoryRoutingResult,
                        max_total_documents: int) -> CrossCategoryResult:
        """Balanced search focusing on primary categories with secondary support"""

        start_time = datetime.now()
        all_documents = []
        documents_per_category = {}

        # Focus on primary categories
        primary_categories = routing_result.primary_categories
        secondary_categories = routing_result.secondary_categories[:2]  # Limit secondary

        # Allocate more documents to primary categories
        primary_allocation = int(max_total_documents * 0.7)
        secondary_allocation = max_total_documents - primary_allocation

        # Retrieve from primary categories
        for category_weight in primary_categories:
            category = category_weight.category
            allocation = max(self.min_documents_per_category,
                           int(primary_allocation / len(primary_categories)))

            category_docs = self._retrieve_from_category(
                query, category, allocation, routing_result.query_analysis
            )
            # Add category metadata
            for doc in category_docs:
                doc.metadata['source_category'] = category
                doc.metadata['is_cross_category'] = True
            all_documents.extend(category_docs)
            documents_per_category[category] = len(category_docs)

        # Retrieve from secondary categories if needed
        if secondary_categories and len(all_documents) < max_total_documents:
            remaining_allocation = max_total_documents - len(all_documents)
            allocation_per_secondary = max(self.min_documents_per_category,
                                        remaining_allocation // len(secondary_categories))

            for category_weight in secondary_categories:
                category = category_weight.category
                category_docs = self._retrieve_from_category(
                    query, category, allocation_per_secondary, routing_result.query_analysis
                )
                # Add category metadata
                for doc in category_docs:
                    doc.metadata['source_category'] = category
                    doc.metadata['is_cross_category'] = True
                all_documents.extend(category_docs)
                documents_per_category[category] = len(category_docs)

        # Score and filter
        scored_docs = score_documents_enhanced_batch(all_documents, query)
        filtered_docs = self._apply_cross_category_filter(
            scored_docs, routing_result.cross_category_threshold
        )

        # Create two-tier result structure
        primary_results, secondary_results = self._create_two_tier_results(
            filtered_docs, routing_result, max_total_documents
        )

        # Create flattened list for backward compatibility
        all_docs_flattened = []
        for section in primary_results + secondary_results:
            all_docs_flattened.extend(section.documents)

        execution_time = (datetime.now() - start_time).total_seconds()

        return CrossCategoryResult(
            primary_results=primary_results,
            secondary_results=secondary_results,
            all_documents=all_docs_flattened,
            category_weights=primary_categories + secondary_categories,
            routing_result=routing_result,
            total_documents_retrieved=len(all_docs_flattened),
            documents_per_category=documents_per_category,
            execution_time=execution_time,
            cache_hit_rate=0.0,
            metadata={'search_type': 'balanced'}
        )
    
    def _focused_search(self, query: str, routing_result: CategoryRoutingResult,
                       max_total_documents: int) -> CrossCategoryResult:
        """Focused search on the most relevant category with minimal cross-category support"""

        start_time = datetime.now()
        all_documents = []
        documents_per_category = {}

        # Focus on the top primary category
        if routing_result.primary_categories:
            primary_category = routing_result.primary_categories[0]
            primary_docs = self._retrieve_from_category(
                query, primary_category.category, max_total_documents, routing_result.query_analysis
            )
            # Add category metadata
            for doc in primary_docs:
                doc.metadata['source_category'] = primary_category.category
                doc.metadata['is_cross_category'] = True
            all_documents.extend(primary_docs)
            documents_per_category[primary_category.category] = len(primary_docs)

        # Add minimal support from secondary categories if needed
        if len(all_documents) < max_total_documents and routing_result.secondary_categories:
            remaining_allocation = max_total_documents - len(all_documents)
            secondary_category = routing_result.secondary_categories[0]

            secondary_docs = self._retrieve_from_category(
                query, secondary_category.category, remaining_allocation, routing_result.query_analysis
            )
            # Add category metadata
            for doc in secondary_docs:
                doc.metadata['source_category'] = secondary_category.category
                doc.metadata['is_cross_category'] = True
            all_documents.extend(secondary_docs)
            documents_per_category[secondary_category.category] = len(secondary_docs)

        # Score and filter
        scored_docs = score_documents_enhanced_batch(all_documents, query)
        filtered_docs = self._apply_cross_category_filter(
            scored_docs, routing_result.cross_category_threshold
        )

        # Create two-tier result structure
        primary_results, secondary_results = self._create_two_tier_results(
            filtered_docs, routing_result, max_total_documents
        )

        # Create flattened list for backward compatibility
        all_docs_flattened = []
        for section in primary_results + secondary_results:
            all_docs_flattened.extend(section.documents)

        execution_time = (datetime.now() - start_time).total_seconds()

        return CrossCategoryResult(
            primary_results=primary_results,
            secondary_results=secondary_results,
            all_documents=all_docs_flattened,
            category_weights=routing_result.primary_categories + routing_result.secondary_categories[:1],
            routing_result=routing_result,
            total_documents_retrieved=len(all_docs_flattened),
            documents_per_category=documents_per_category,
            execution_time=execution_time,
            cache_hit_rate=0.0,
            metadata={'search_type': 'focused'}
        )
    
    def _retrieve_from_category(self, query: str, category: str, allocation: int,
                              query_analysis) -> List[Document]:
        """Retrieve documents from a specific category"""
        
        try:
            # Choose retrieval method based on query analysis
            if query_analysis.complexity == 'high' or len(query_analysis.domain_keywords) > 3:
                # Use multi-stage retrieval for complex queries
                result = retrieve_documents_multi_stage(
                    query=query,
                    category=category,
                    k=allocation,
                    query_analysis=query_analysis
                )
                return result.documents
            else:
                # Use hybrid search for simpler queries
                result = hybrid_search(
                    query=query,
                    category=category,
                    k=allocation,
                    strategies=['semantic', 'keyword']
                )
                return result.documents
                
        except Exception as e:
            logger.error(f"Error retrieving from category {category}: {e}")
            # Fallback to basic similarity search
            try:
                docs = get_unified_vector_db().similarity_search(
                    query=query,
                    category=category,
                    k=allocation
                )
                return docs
            except Exception as fallback_error:
                logger.error(f"Fallback retrieval failed for category {category}: {fallback_error}")
                return []
    
    def _calculate_category_allocations(self, category_weights: List[CategoryWeight],
                                      max_total_documents: int) -> Dict[str, int]:
        """Calculate document allocation for each category based on weights"""
        
        allocations = {}
        total_weight = sum(cw.weight for cw in category_weights)
        
        if total_weight == 0:
            # Equal distribution if no weights
            allocation_per_category = max_total_documents // len(category_weights)
            for cw in category_weights:
                allocations[cw.category] = allocation_per_category
        else:
            # Weighted distribution
            for cw in category_weights:
                allocation = int((cw.weight / total_weight) * max_total_documents)
                allocations[cw.category] = max(self.min_documents_per_category,
                                             min(allocation, self.max_documents_per_category))
        
        return allocations
    
    def _apply_cross_category_filter(self, scored_docs: List[ScoredDocument],
                                   threshold: float) -> List[Document]:
        """Apply cross-category relevance threshold filtering"""

        filtered_docs = []
        for scored_doc in scored_docs:
            if scored_doc.relevance_score.overall_score >= threshold:
                # Add cross-category metadata
                doc = scored_doc.document
                doc.metadata['cross_category_score'] = scored_doc.relevance_score.overall_score
                doc.metadata['cross_category_threshold'] = threshold
                filtered_docs.append(doc)

        return filtered_docs

    def _create_two_tier_results(self, documents: List[Document],
                               routing_result: CategoryRoutingResult,
                               max_total_documents: int) -> Tuple[List[CategoryResultSection], List[CategoryResultSection]]:
        """Create two-tier result structure with primary categories first"""

        # Group documents by category
        docs_by_category = {}
        for doc in documents:
            category = doc.metadata.get('source_category', 'unknown')
            if category not in docs_by_category:
                docs_by_category[category] = []
            docs_by_category[category].append(doc)

        # Create primary results (from primary categories)
        primary_results = []
        primary_doc_count = 0

        for category_weight in routing_result.primary_categories:
            category = category_weight.category
            if category in docs_by_category:
                category_docs = docs_by_category[category]
                # Limit documents per primary category to ensure balanced distribution
                max_per_primary = min(len(category_docs), max_total_documents // 2)
                category_docs = category_docs[:max_per_primary]

                primary_results.append(CategoryResultSection(
                    category=category,
                    documents=category_docs,
                    is_primary=True,
                    weight=category_weight.weight,
                    document_count=len(category_docs)
                ))
                primary_doc_count += len(category_docs)

        # Create secondary results (from secondary categories)
        secondary_results = []
        remaining_slots = max_total_documents - primary_doc_count

        for category_weight in routing_result.secondary_categories:
            if remaining_slots <= 0:
                break

            category = category_weight.category
            if category in docs_by_category:
                category_docs = docs_by_category[category]
                # Limit documents for secondary categories
                max_per_secondary = min(len(category_docs), remaining_slots // max(1, len(routing_result.secondary_categories)))
                category_docs = category_docs[:max_per_secondary]

                if category_docs:  # Only add if we have documents
                    secondary_results.append(CategoryResultSection(
                        category=category,
                        documents=category_docs,
                        is_primary=False,
                        weight=category_weight.weight,
                        document_count=len(category_docs)
                    ))
                    remaining_slots -= len(category_docs)

        return primary_results, secondary_results
    
    def _calculate_cache_hit_rate(self, query: str, routing_result: CategoryRoutingResult) -> float:
        """Calculate cache hit rate for the retrieval process"""
        
        # This is a simplified calculation - in practice, you'd track actual cache hits
        cache_key = f"cross_category:{hash(query)}"
        cached_result = cache_service.get(cache_key)
        
        if cached_result:
            return 1.0
        else:
            return 0.0
    
    def _create_fallback_result(self, query: str, start_time: datetime) -> CrossCategoryResult:
        """Create a fallback result when retrieval fails"""

        # Try basic retrieval from CANOPY category
        try:
            docs = get_unified_vector_db().similarity_search(query=query, category='CANOPY', k=10)
            # Add category metadata
            for doc in docs:
                doc.metadata['source_category'] = 'CANOPY'
                doc.metadata['is_cross_category'] = True
        except Exception:
            docs = []

        # Create fallback result sections
        primary_results = []
        secondary_results = []

        if docs:
            primary_results.append(CategoryResultSection(
                category='CANOPY',
                documents=docs,
                is_primary=True,
                weight=1.0,
                document_count=len(docs)
            ))

        return CrossCategoryResult(
            primary_results=primary_results,
            secondary_results=secondary_results,
            all_documents=docs,
            category_weights=[],
            routing_result=None,
            total_documents_retrieved=len(docs),
            documents_per_category={'CANOPY': len(docs)},
            execution_time=(datetime.now() - start_time).total_seconds(),
            cache_hit_rate=0.0,
            metadata={'error': True, 'fallback': True}
        )

# Global instance
cross_category_retriever = CrossCategoryRetriever()

def retrieve_cross_category(query: str, user_context: Optional[Dict[str, Any]] = None,
                          max_total_documents: int = 30) -> CrossCategoryResult:
    """
    Retrieve documents from multiple categories using intelligent routing.
    
    Args:
        query: The user query
        user_context: Optional user context for personalized routing
        max_total_documents: Maximum total documents to return
        
    Returns:
        CrossCategoryResult with documents and metadata
    """
    return cross_category_retriever.retrieve_cross_category(
        query, user_context, max_total_documents
    )

def get_category_statistics() -> Dict[str, Dict[str, Any]]:
    """Get category statistics for analysis"""
    from app.services.category_router import category_router
    return category_router.get_category_statistics() 