#!/usr/bin/env python3
"""
Quick test script to verify the algae search fix.
This script tests both single-category and cross-category search for the "algae" query.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_algae_single_category():
    """Test algae search in CANOPY category (single-category mode)."""
    print("🧪 Testing Algae Search in CANOPY Category (Single-Category Mode)")
    print("="*60)
    
    try:
        from app.services.query_service import query_category
        
        # Test the exact query that was failing
        result = query_category(
            category="CANOPY",
            question="algae",
            anti_hallucination_mode="balanced",
            use_cross_category=False  # Explicitly disable cross-category
        )
        
        print("✅ Query completed successfully")
        
        if isinstance(result, dict):
            sources = result.get("sources", [])
            answer = result.get("answer", "")
            metadata = result.get("metadata", {})
            
            print(f"📊 Sources found: {len(sources)}")
            print(f"📝 Answer preview: {answer[:100]}...")
            
            # Check if cross-category was incorrectly triggered
            if metadata.get("is_cross_category"):
                print("❌ ERROR: Single-category search was treated as cross-category!")
                return False
            else:
                print("✅ Correctly identified as single-category search")
            
            # Check if we got a meaningful result
            if sources:
                print("✅ Found sources for algae query")
                for i, source in enumerate(sources[:3]):
                    print(f"   {i+1}. {source.get('display_name', 'Unknown')} (Category: {source.get('category', 'Unknown')})")
                return True
            elif "don't have any information" in answer.lower():
                print("⚠️ No sources found, but got proper 'no information' response")
                return True
            else:
                print("✅ Got response without sources (might be expected)")
                return True
        else:
            print(f"❌ Unexpected result type: {type(result)}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_algae_cross_category():
    """Test algae search in cross-category mode."""
    print("\n🧪 Testing Algae Search in Cross-Category Mode")
    print("="*60)
    
    try:
        from app.services.query_service import query_category
        
        # Test cross-category search
        result = query_category(
            category="ALL_CATEGORIES",  # Special identifier for cross-category
            question="algae",
            anti_hallucination_mode="balanced",
            use_cross_category=True  # Explicitly enable cross-category
        )
        
        print("✅ Cross-category query completed successfully")
        
        if isinstance(result, dict):
            sources = result.get("sources", [])
            answer = result.get("answer", "")
            metadata = result.get("metadata", {})
            
            print(f"📊 Sources found: {len(sources)}")
            print(f"📝 Answer preview: {answer[:100]}...")
            
            # Check if cross-category was correctly triggered
            if metadata.get("is_cross_category"):
                print("✅ Correctly identified as cross-category search")
                
                # Check for cross-category metadata
                primary_cats = metadata.get("primary_categories", [])
                secondary_cats = metadata.get("secondary_categories", [])
                
                print(f"📊 Primary categories: {len(primary_cats)}")
                print(f"📊 Secondary categories: {len(secondary_cats)}")
                
                if primary_cats or secondary_cats:
                    print("✅ Cross-category metadata present")
                    for cat in primary_cats:
                        print(f"   Primary: {cat.get('category')} ({cat.get('document_count')} docs)")
                    for cat in secondary_cats:
                        print(f"   Secondary: {cat.get('category')} ({cat.get('document_count')} docs)")
                
                return True
            else:
                print("❌ ERROR: Cross-category search not properly identified!")
                return False
        else:
            print(f"❌ Unexpected result type: {type(result)}")
            return False
            
    except Exception as e:
        print(f"❌ Cross-category test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """Test the configuration to ensure it's loaded correctly."""
    print("\n🧪 Testing Configuration")
    print("="*60)
    
    try:
        from config.settings.query_config import get_query_config
        
        config = get_query_config()
        print(f"✅ Configuration loaded successfully")
        print(f"📊 enable_cross_category_search: {config.enable_cross_category_search}")
        print(f"📊 cross_category_threshold: {config.cross_category_threshold}")
        print(f"📊 max_categories_per_search: {config.max_categories_per_search}")
        print(f"📊 retrieval_k: {config.retrieval_k}")
        print(f"📊 max_documents: {config.max_documents}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Algae Search Fix")
    print("="*60)
    
    tests = [
        ("Configuration", test_configuration),
        ("Algae Single-Category Search", test_algae_single_category),
        ("Algae Cross-Category Search", test_algae_cross_category),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print('='*60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The algae search fix is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
