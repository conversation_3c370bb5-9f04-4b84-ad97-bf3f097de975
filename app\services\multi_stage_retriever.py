"""
Multi-Stage Retrieval Service
Implements sophisticated multi-stage retrieval pipeline for improved precision and recall.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import numpy as np
from langchain.schema import Document
from langchain_ollama.embeddings import OllamaEmbeddings
from app.utils.performance_monitor import performance_monitor
from app.services.semantic_analyzer import QueryIntent, QueryAnalysis
from app.services.enhanced_relevance_scorer import EnhancedRelevanceScorer, ScoredDocument
from app.services.vector_db import similarity_search_with_category_filter

logger = logging.getLogger(__name__)

@dataclass
class RetrievalStage:
    """Information about a retrieval stage"""
    stage_name: str
    documents_retrieved: int
    documents_filtered: int
    execution_time: float
    stage_metadata: Dict[str, Any]

@dataclass
class MultiStageResult:
    """Result from multi-stage retrieval"""
    final_documents: List[Document]
    stage_results: List[RetrievalStage]
    total_execution_time: float
    retrieval_metadata: Dict[str, Any]

class MultiStageRetriever:
    """
    Multi-stage retrieval system with sophisticated pipeline
    """
    
    def __init__(self, embedding_model: str = None):
        self.embedding_model = embedding_model or "mxbai-embed-large:latest"
        self._embedding_function = None
        self._enhanced_scorer = None
        
        # Stage configuration
        self.stage_config = {
            'stage1_broad_k_multiplier': 2.0,  # Retrieve 2x more documents initially
            'stage2_rerank_top_n': 20,         # Top N documents to re-rank
            'stage3_final_top_n': 8,           # Final number of documents
            'stage1_min_k': 8,                 # Minimum k for stage 1
            'stage1_max_k': 30,                # Maximum k for stage 1
            'semantic_threshold': 0.3,         # Minimum semantic similarity
            'diversity_threshold': 0.7,        # Diversity threshold for deduplication
        }
        
        # Performance tracking
        self.stage_times = []
        self.stage_document_counts = []
    
    def _get_embedding_function(self):
        """Get or create the embedding function"""
        if self._embedding_function is None:
            try:
                self._embedding_function = OllamaEmbeddings(model=self.embedding_model)
                logger.info(f"Initialized embedding function with model: {self.embedding_model}")
            except Exception as e:
                logger.error(f"Failed to initialize embedding function: {str(e)}")
                raise
        return self._embedding_function
    
    def _get_enhanced_scorer(self):
        """Get or create the enhanced scorer"""
        if self._enhanced_scorer is None:
            self._enhanced_scorer = EnhancedRelevanceScorer(self.embedding_model)
        return self._enhanced_scorer
    
    @performance_monitor(track_memory=True, track_cpu=True)
    def retrieve_documents(self, query: str, category: str, 
                          query_analysis: QueryAnalysis = None,
                          base_k: int = 12) -> MultiStageResult:
        """
        Perform multi-stage document retrieval
        
        Args:
            query: User's query
            category: Document category
            query_analysis: Optional query analysis results
            base_k: Base retrieval k value
            
        Returns:
            MultiStageResult with final documents and stage information
        """
        import time
        start_time = time.time()
        stage_results = []
        
        try:
            # Stage 1: Broad retrieval
            stage1_start = time.time()
            stage1_docs = self._stage1_broad_retrieval(query, category, base_k, query_analysis)
            stage1_time = time.time() - stage1_start
            
            stage_results.append(RetrievalStage(
                stage_name="Broad Retrieval",
                documents_retrieved=len(stage1_docs),
                documents_filtered=len(stage1_docs),
                execution_time=stage1_time,
                stage_metadata={'k_used': len(stage1_docs)}
            ))
            
            if not stage1_docs:
                logger.warning("No documents retrieved in stage 1")
                return self._create_empty_result(stage_results, time.time() - start_time)
            
            # Stage 2: Semantic re-ranking
            stage2_start = time.time()
            stage2_docs = self._stage2_semantic_reranking(
                stage1_docs, query, query_analysis
            )
            stage2_time = time.time() - stage2_start
            
            stage_results.append(RetrievalStage(
                stage_name="Semantic Re-ranking",
                documents_retrieved=len(stage2_docs),
                documents_filtered=len(stage2_docs),
                execution_time=stage2_time,
                stage_metadata={'reranked_count': len(stage2_docs)}
            ))
            
            # Stage 3: Contextual filtering and final ranking
            stage3_start = time.time()
            final_docs = self._stage3_contextual_filtering(
                stage2_docs, query, query_analysis
            )
            stage3_time = time.time() - stage3_start
            
            stage_results.append(RetrievalStage(
                stage_name="Contextual Filtering",
                documents_retrieved=len(final_docs),
                documents_filtered=len(final_docs),
                execution_time=stage3_time,
                stage_metadata={'final_count': len(final_docs)}
            ))
            
            total_time = time.time() - start_time
            
            # Create retrieval metadata
            retrieval_metadata = {
                'total_stages': len(stage_results),
                'total_documents_processed': sum(s.documents_retrieved for s in stage_results),
                'final_document_count': len(final_docs),
                'query_analysis_used': query_analysis is not None,
                'embedding_model': self.embedding_model,
                'retrieval_timestamp': datetime.now().isoformat()
            }
            
            return MultiStageResult(
                final_documents=final_docs,
                stage_results=stage_results,
                total_execution_time=total_time,
                retrieval_metadata=retrieval_metadata
            )
            
        except Exception as e:
            logger.error(f"Error in multi-stage retrieval: {str(e)}")
            return self._create_empty_result(stage_results, time.time() - start_time)
    
    def _stage1_broad_retrieval(self, query: str, category: str, base_k: int,
                               query_analysis: QueryAnalysis = None) -> List[Document]:
        """Stage 1: Broad retrieval with higher k value"""
        try:
            # Calculate k for broad retrieval
            if query_analysis and hasattr(query_analysis.complexity, 'suggested_k'):
                suggested_k = query_analysis.complexity.suggested_k
            else:
                suggested_k = base_k
            
            # Apply multiplier for broad retrieval
            broad_k = int(suggested_k * self.stage_config['stage1_broad_k_multiplier'])
            broad_k = max(self.stage_config['stage1_min_k'], 
                         min(self.stage_config['stage1_max_k'], broad_k))
            
            logger.info(f"Stage 1: Broad retrieval with k={broad_k}")
            
            # Perform broad similarity search
            docs = similarity_search_with_category_filter(
                query=query,
                category=category,
                k=broad_k
            )
            
            logger.info(f"Stage 1: Retrieved {len(docs)} documents")
            return docs
            
        except Exception as e:
            logger.error(f"Error in stage 1 broad retrieval: {str(e)}")
            return []
    
    def _stage2_semantic_reranking(self, docs: List[Document], query: str,
                                 query_analysis: QueryAnalysis = None) -> List[Document]:
        """Stage 2: Semantic re-ranking using enhanced scoring"""
        try:
            if not docs:
                return []
            
            # Get query intent and domain keywords
            query_intent = QueryIntent.UNKNOWN
            domain_keywords = []
            
            if query_analysis:
                query_intent = query_analysis.complexity.intent
                domain_keywords = query_analysis.domain_keywords
            
            # Use enhanced scorer for semantic re-ranking
            enhanced_scorer = self._get_enhanced_scorer()
            
            # Score all documents
            scored_docs = enhanced_scorer.score_documents_batch(
                docs=docs,
                question=query,
                query_intent=query_intent,
                domain_keywords=domain_keywords
            )
            
            # Filter by semantic threshold
            filtered_docs = []
            for scored_doc in scored_docs:
                if scored_doc.relevance_score.semantic_score >= self.stage_config['semantic_threshold']:
                    filtered_docs.append(scored_doc.document)
            
            # Limit to top N documents
            top_n = min(self.stage_config['stage2_rerank_top_n'], len(filtered_docs))
            final_docs = filtered_docs[:top_n]
            
            logger.info(f"Stage 2: Re-ranked {len(docs)} documents, kept {len(final_docs)}")
            return final_docs
            
        except Exception as e:
            logger.error(f"Error in stage 2 semantic re-ranking: {str(e)}")
            return docs[:self.stage_config['stage2_rerank_top_n']]  # Fallback to original docs
    
    def _stage3_contextual_filtering(self, docs: List[Document], query: str,
                                   query_analysis: QueryAnalysis = None) -> List[Document]:
        """Stage 3: Contextual filtering and final ranking"""
        try:
            if not docs:
                return []
            
            # Get query intent and domain keywords
            query_intent = QueryIntent.UNKNOWN
            domain_keywords = []
            
            if query_analysis:
                query_intent = query_analysis.complexity.intent
                domain_keywords = query_analysis.domain_keywords
            
            # Apply intent-specific filtering
            filtered_docs = self._apply_intent_filtering(docs, query_intent)
            
            # Apply diversity filtering to remove similar documents
            diverse_docs = self._apply_diversity_filtering(filtered_docs, query)
            
            # Final ranking with enhanced scoring
            enhanced_scorer = self._get_enhanced_scorer()
            final_scored_docs = enhanced_scorer.score_documents_batch(
                docs=diverse_docs,
                question=query,
                query_intent=query_intent,
                domain_keywords=domain_keywords
            )
            
            # Extract final documents
            final_docs = [scored_doc.document for scored_doc in final_scored_docs]
            
            # Limit to final top N
            top_n = min(self.stage_config['stage3_final_top_n'], len(final_docs))
            final_docs = final_docs[:top_n]
            
            logger.info(f"Stage 3: Filtered to {len(final_docs)} final documents")
            return final_docs
            
        except Exception as e:
            logger.error(f"Error in stage 3 contextual filtering: {str(e)}")
            return docs[:self.stage_config['stage3_final_top_n']]  # Fallback
    
    def _apply_intent_filtering(self, docs: List[Document], query_intent: QueryIntent) -> List[Document]:
        """Apply intent-specific filtering"""
        if query_intent == QueryIntent.UNKNOWN:
            return docs
        
        filtered_docs = []
        
        for doc in docs:
            content_lower = doc.page_content.lower()
            metadata_lower = str(doc.metadata).lower()
            
            # Intent-specific filtering rules
            if query_intent == QueryIntent.FACTUAL:
                # Prefer documents with definitions and clear explanations
                if any(term in content_lower for term in ['definition', 'defined', 'refers to', 'means']):
                    filtered_docs.append(doc)
            
            elif query_intent == QueryIntent.ANALYTICAL:
                # Prefer documents with methodology and analysis
                if any(term in content_lower for term in ['method', 'analysis', 'study', 'research', 'investigation']):
                    filtered_docs.append(doc)
            
            elif query_intent == QueryIntent.COMPARATIVE:
                # Prefer documents with comparison language
                if any(term in content_lower for term in ['compare', 'versus', 'difference', 'similar', 'contrast']):
                    filtered_docs.append(doc)
            
            elif query_intent == QueryIntent.QUANTITATIVE:
                # Prefer documents with numbers and statistics
                if any(term in content_lower for term in ['percent', 'number', 'statistics', 'data', 'results']):
                    filtered_docs.append(doc)
            
            else:
                # For other intents, include all documents
                filtered_docs.append(doc)
        
        # If filtering removed too many documents, return original list
        if len(filtered_docs) < len(docs) * 0.3:  # Less than 30% kept
            logger.warning(f"Intent filtering too aggressive, returning original documents")
            return docs
        
        return filtered_docs
    
    def _apply_diversity_filtering(self, docs: List[Document], query: str) -> List[Document]:
        """Apply diversity filtering to remove similar documents"""
        if len(docs) <= 1:
            return docs
        
        try:
            # Get embeddings for documents
            embed_fn = self._get_embedding_function()
            
            # Calculate embeddings for first 500 characters of each document
            doc_embeddings = []
            for doc in docs:
                content_sample = doc.page_content[:500]
                embedding = embed_fn.embed_query(content_sample)
                doc_embeddings.append(embedding)
            
            # Remove similar documents
            diverse_docs = [docs[0]]  # Keep first document
            
            for i in range(1, len(docs)):
                current_embedding = doc_embeddings[i]
                is_diverse = True
                
                # Check similarity with already selected documents
                for j in range(len(diverse_docs)):
                    selected_idx = docs.index(diverse_docs[j])
                    selected_embedding = doc_embeddings[selected_idx]
                    
                    similarity = self._cosine_similarity(current_embedding, selected_embedding)
                    
                    if similarity > self.stage_config['diversity_threshold']:
                        is_diverse = False
                        break
                
                if is_diverse:
                    diverse_docs.append(docs[i])
            
            logger.info(f"Diversity filtering: {len(docs)} -> {len(diverse_docs)} documents")
            return diverse_docs
            
        except Exception as e:
            logger.warning(f"Error in diversity filtering: {str(e)}, returning original documents")
            return docs
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors"""
        try:
            vec1 = np.array(vec1)
            vec2 = np.array(vec2)
            
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return dot_product / (norm1 * norm2)
            
        except Exception as e:
            logger.warning(f"Failed to calculate cosine similarity: {str(e)}")
            return 0.0
    
    def _create_empty_result(self, stage_results: List[RetrievalStage], 
                           total_time: float) -> MultiStageResult:
        """Create empty result when retrieval fails"""
        return MultiStageResult(
            final_documents=[],
            stage_results=stage_results,
            total_execution_time=total_time,
            retrieval_metadata={
                'error': 'Retrieval failed',
                'total_stages': len(stage_results),
                'final_document_count': 0
            }
        )

# Global instance for easy access
_multi_stage_retriever = None

def get_multi_stage_retriever(embedding_model: str = None) -> MultiStageRetriever:
    """Get or create global multi-stage retriever instance"""
    global _multi_stage_retriever
    if _multi_stage_retriever is None:
        _multi_stage_retriever = MultiStageRetriever(embedding_model)
    return _multi_stage_retriever

def retrieve_documents_multi_stage(query: str, category: str, 
                                 query_analysis: QueryAnalysis = None,
                                 base_k: int = 12) -> MultiStageResult:
    """Convenience function for multi-stage document retrieval"""
    retriever = get_multi_stage_retriever()
    return retriever.retrieve_documents(query, category, query_analysis, base_k) 