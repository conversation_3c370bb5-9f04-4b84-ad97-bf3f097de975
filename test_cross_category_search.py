#!/usr/bin/env python3
"""
Test script for cross-category search functionality.

This script tests the enhanced cross-category search implementation including:
- Two-tier result ranking (primary categories first, then secondary)
- Category badges and visual separation
- Cross-category API endpoint
- Result formatting with category metadata
"""

import sys
import os
import json
import requests
import time
from typing import Dict, Any

# Add the app directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_cross_category_api_endpoint():
    """Test the cross-category API endpoint."""
    print("🧪 Testing Cross-Category API Endpoint...")
    
    # Test data
    test_query = "forest management practices"
    
    # API endpoint
    url = "http://localhost:5000/query/cross-category"
    
    payload = {
        "query": test_query,
        "anti_hallucination_mode": "balanced",
        "client_name": "Test User"
    }
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Cross-category API endpoint is working")
            
            # Check for cross-category metadata
            if result.get("is_cross_category"):
                print("✅ Cross-category flag is set")
            else:
                print("❌ Cross-category flag is missing")
            
            # Check metadata structure
            metadata = result.get("metadata", {})
            if metadata.get("is_cross_category"):
                print("✅ Cross-category metadata is present")
                
                # Check for primary and secondary categories
                primary_cats = metadata.get("primary_categories", [])
                secondary_cats = metadata.get("secondary_categories", [])
                
                print(f"📊 Primary categories: {len(primary_cats)}")
                print(f"📊 Secondary categories: {len(secondary_cats)}")
                
                if primary_cats:
                    print("✅ Primary categories found")
                    for cat in primary_cats:
                        print(f"   - {cat.get('category')}: {cat.get('document_count')} docs (weight: {cat.get('weight', 0):.2f})")
                
                if secondary_cats:
                    print("✅ Secondary categories found")
                    for cat in secondary_cats:
                        print(f"   - {cat.get('category')}: {cat.get('document_count')} docs (weight: {cat.get('weight', 0):.2f})")
                
            else:
                print("❌ Cross-category metadata is missing")
            
            # Check sources for category information
            sources = result.get("sources", [])
            if sources:
                print(f"📚 Found {len(sources)} sources")
                sources_with_categories = [s for s in sources if s.get("category")]
                print(f"✅ {len(sources_with_categories)} sources have category information")
            
            return True
            
        else:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API request failed: {e}")
        return False

def test_cross_category_service():
    """Test the cross-category service directly."""
    print("\n🧪 Testing Cross-Category Service...")
    
    try:
        from app.services.cross_category_retriever import retrieve_cross_category
        
        # Test query
        test_query = "sustainable forest management"
        
        # Call the service
        result = retrieve_cross_category(
            query=test_query,
            user_context={"user_id": 1},
            max_total_documents=20
        )
        
        print("✅ Cross-category service is working")
        print(f"📊 Total documents retrieved: {result.total_documents_retrieved}")
        print(f"📊 Primary result sections: {len(result.primary_results)}")
        print(f"📊 Secondary result sections: {len(result.secondary_results)}")
        print(f"⏱️ Execution time: {result.execution_time:.3f}s")
        
        # Check two-tier structure
        if result.primary_results:
            print("✅ Primary results found:")
            for section in result.primary_results:
                print(f"   - {section.category}: {section.document_count} docs (primary: {section.is_primary})")
        
        if result.secondary_results:
            print("✅ Secondary results found:")
            for section in result.secondary_results:
                print(f"   - {section.category}: {section.document_count} docs (primary: {section.is_primary})")
        
        # Check document metadata
        if result.all_documents:
            docs_with_category = [doc for doc in result.all_documents if doc.metadata.get("source_category")]
            print(f"✅ {len(docs_with_category)} documents have category metadata")
        
        return True
        
    except Exception as e:
        print(f"❌ Cross-category service test failed: {e}")
        return False

def test_category_router():
    """Test the category routing functionality."""
    print("\n🧪 Testing Category Router...")
    
    try:
        from app.services.category_router import route_query_to_categories
        
        # Test query
        test_query = "forest canopy research"
        
        # Call the router
        routing_result = route_query_to_categories(test_query)
        
        print("✅ Category router is working")
        print(f"📊 Routing strategy: {routing_result.routing_strategy}")
        print(f"📊 Primary categories: {len(routing_result.primary_categories)}")
        print(f"📊 Secondary categories: {len(routing_result.secondary_categories)}")
        print(f"🎯 Cross-category threshold: {routing_result.cross_category_threshold}")
        
        # Display category weights
        if routing_result.primary_categories:
            print("Primary categories:")
            for cat in routing_result.primary_categories:
                print(f"   - {cat.category}: weight={cat.weight:.3f}, confidence={cat.confidence:.3f}")
        
        if routing_result.secondary_categories:
            print("Secondary categories:")
            for cat in routing_result.secondary_categories:
                print(f"   - {cat.category}: weight={cat.weight:.3f}, confidence={cat.confidence:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Category router test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Cross-Category Search Tests\n")
    
    tests = [
        ("Category Router", test_category_router),
        ("Cross-Category Service", test_cross_category_service),
        ("Cross-Category API Endpoint", test_cross_category_api_endpoint),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Cross-category search is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
