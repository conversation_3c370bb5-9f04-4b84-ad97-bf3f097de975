"""
Advanced Caching Service for ERDB Document Management System
Implements intelligent caching strategies, predictive caching, and cache warming
"""

import json
import hashlib
import os
import time
import logging
from datetime import datetime, timedelta
from typing import Any, Optional, Dict, List, Set, Tuple
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from functools import wraps
import threading
import asyncio
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

# Redis imports with fallback to in-memory cache
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger.warning("Redis not available, falling back to in-memory cache")

@dataclass
class CacheEntry:
    """Enhanced cache entry with metadata"""
    value: Any
    created: datetime
    last_accessed: datetime
    access_count: int
    ttl: int
    priority: int = 1  # Higher priority = less likely to be evicted
    category: str = "default"
    tags: Set[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = set()
        if self.last_accessed is None:
            self.last_accessed = self.created

@dataclass
class CacheAccessPattern:
    """Pattern of cache access for predictive caching"""
    key_pattern: str
    access_count: int
    last_accessed: datetime
    related_keys: Set[str]
    time_of_day_pattern: Dict[int, int]  # Hour -> count
    day_of_week_pattern: Dict[int, int]  # 0-6 -> count

class IntelligentCacheService:
    """Advanced caching service with intelligent strategies"""
    
    def __init__(self, use_redis: bool = True, max_memory_mb: int = 512):
        self._cache = {}
        self._cache_metadata = {}
        self._access_patterns = defaultdict(lambda: CacheAccessPattern(
            key_pattern="", access_count=0, last_accessed=datetime.now(),
            related_keys=set(), time_of_day_pattern=defaultdict(int),
            day_of_week_pattern=defaultdict(int)
        ))
        self._default_ttl = 3600  # 1 hour default
        self._redis_client = None
        self._use_redis = use_redis and REDIS_AVAILABLE
        self._max_memory_bytes = max_memory_mb * 1024 * 1024
        self._current_memory_usage = 0
        self._cache_hits = 0
        self._cache_misses = 0
        self._predictive_cache_hits = 0
        
        # LRU tracking
        self._lru_order = deque()
        self._lru_map = {}
        
        # Predictive caching
        self._prediction_queue = deque(maxlen=1000)
        self._warmup_queue = deque(maxlen=100)
        
        # Threading
        self._lock = threading.RLock()
        self._executor = ThreadPoolExecutor(max_workers=4)
        
        # Initialize Redis connection if available
        if self._use_redis:
            try:
                redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/2')  # Use DB 2 for advanced cache
                self._redis_client = redis.from_url(redis_url, decode_responses=True)
                self._redis_client.ping()
                logger.info("Redis advanced cache initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize Redis advanced cache: {e}, falling back to in-memory")
                self._use_redis = False
                self._redis_client = None
        
        # Start background tasks
        self._start_background_tasks()
    
    def _start_background_tasks(self):
        """Start background tasks for cache maintenance"""
        def cleanup_task():
            while True:
                try:
                    time.sleep(300)  # Run every 5 minutes
                    self._cleanup_expired_entries()
                    self._analyze_access_patterns()
                    self._predict_and_warm_cache()
                except Exception as e:
                    logger.error(f"Background cleanup task error: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
        cleanup_thread.start()
        logger.info("Background cache maintenance tasks started")
    
    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate cache key from prefix and arguments"""
        key_data = f"{prefix}:{args}:{sorted(kwargs.items())}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, key: str, update_access: bool = True) -> Optional[Any]:
        """Get value from cache with intelligent access tracking"""
        try:
            if self._use_redis and self._redis_client:
                # Try Redis first
                cached_data = self._redis_client.get(key)
                if cached_data:
                    self._cache_hits += 1
                    if update_access:
                        self._update_access_pattern(key)
                    return json.loads(cached_data)
                else:
                    self._cache_misses += 1
                    return None
            else:
                # Fallback to in-memory cache
                with self._lock:
                    if key not in self._cache:
                        self._cache_misses += 1
                        return None

                    # Check if expired
                    if key in self._cache_metadata:
                        expiry = self._cache_metadata[key].get('expiry')
                        if expiry and datetime.now() > expiry:
                            self.delete(key)
                            self._cache_misses += 1
                            return None

                    self._cache_hits += 1
                    
                    # Update access tracking
                    if update_access:
                        self._update_access_pattern(key)
                        self._update_lru_order(key)
                    
                    return self._cache[key]
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            self._cache_misses += 1
            return None
    
    def set(self, key: str, value: Any, ttl: int = None, priority: int = 1, 
            category: str = "default", tags: Set[str] = None) -> None:
        """Set value in cache with enhanced metadata"""
        try:
            ttl = ttl or self._default_ttl
            tags = tags or set()
            
            # Check memory limits
            self._check_memory_limits()
            
            if self._use_redis and self._redis_client:
                # Store in Redis with enhanced metadata
                entry_data = {
                    'value': value,
                    'created': datetime.now().isoformat(),
                    'priority': priority,
                    'category': category,
                    'tags': list(tags)
                }
                serialized_data = json.dumps(entry_data, default=str)
                self._redis_client.setex(key, ttl, serialized_data)
            else:
                # Fallback to in-memory cache
                with self._lock:
                    expiry = datetime.now() + timedelta(seconds=ttl)
                    
                    self._cache[key] = value
                    self._cache_metadata[key] = {
                        'created': datetime.now(),
                        'expiry': expiry,
                        'ttl': ttl,
                        'priority': priority,
                        'category': category,
                        'tags': tags,
                        'access_count': 0,
                        'last_accessed': datetime.now()
                    }
                    
                    # Update LRU tracking
                    self._update_lru_order(key)
                    
                    # Update memory usage
                    self._update_memory_usage(key, value)
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
    
    def _update_access_pattern(self, key: str):
        """Update access pattern for predictive caching"""
        now = datetime.now()
        pattern = self._access_patterns[key]
        
        pattern.access_count += 1
        pattern.last_accessed = now
        pattern.time_of_day_pattern[now.hour] += 1
        pattern.day_of_week_pattern[now.weekday()] += 1
        
        # Add to prediction queue
        self._prediction_queue.append((key, now))
    
    def _update_lru_order(self, key: str):
        """Update LRU order for eviction"""
        if key in self._lru_map:
            self._lru_order.remove(key)
        self._lru_order.append(key)
        self._lru_map[key] = len(self._lru_order) - 1
    
    def _check_memory_limits(self):
        """Check and enforce memory limits"""
        if self._current_memory_usage > self._max_memory_bytes:
            self._evict_entries()
    
    def _evict_entries(self):
        """Intelligent cache eviction based on priority and LRU"""
        with self._lock:
            # Calculate eviction scores
            eviction_scores = []
            for key in self._cache.keys():
                metadata = self._cache_metadata.get(key, {})
                priority = metadata.get('priority', 1)
                access_count = metadata.get('access_count', 0)
                last_accessed = metadata.get('last_accessed', datetime.now())
                
                # Score based on priority, access count, and recency
                time_factor = (datetime.now() - last_accessed).total_seconds() / 3600  # hours
                score = (time_factor * 0.6) + (1.0 / priority * 0.3) + (1.0 / (access_count + 1) * 0.1)
                eviction_scores.append((key, score))
            
            # Sort by eviction score (highest first)
            eviction_scores.sort(key=lambda x: x[1], reverse=True)
            
            # Evict entries until under memory limit
            evicted_count = 0
            for key, _ in eviction_scores:
                if self._current_memory_usage <= self._max_memory_bytes * 0.8:  # Leave 20% buffer
                    break
                
                self.delete(key)
                evicted_count += 1
            
            if evicted_count > 0:
                logger.info(f"Evicted {evicted_count} cache entries due to memory limits")
    
    def _update_memory_usage(self, key: str, value: Any):
        """Update memory usage tracking"""
        try:
            value_size = len(str(value))
            self._current_memory_usage += value_size
        except Exception:
            # Fallback estimation
            self._current_memory_usage += 1024  # 1KB estimate
    
    def _cleanup_expired_entries(self):
        """Remove expired entries"""
        expired_keys = []
        with self._lock:
            for key, metadata in self._cache_metadata.items():
                if metadata.get('expiry') and datetime.now() > metadata['expiry']:
                    expired_keys.append(key)
            
            for key in expired_keys:
                self.delete(key)
        
        if expired_keys:
            logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    def _analyze_access_patterns(self):
        """Analyze access patterns for predictive caching"""
        # Analyze time-based patterns
        current_hour = datetime.now().hour
        current_weekday = datetime.now().weekday()
        
        for key, pattern in self._access_patterns.items():
            # Check if this key is typically accessed at this time
            if pattern.time_of_day_pattern.get(current_hour, 0) > 2:  # Accessed more than twice at this hour
                self._warmup_queue.append(key)
            
            # Check if this key is typically accessed on this day
            if pattern.day_of_week_pattern.get(current_weekday, 0) > 1:  # Accessed more than once on this day
                self._warmup_queue.append(key)
    
    def _predict_and_warm_cache(self):
        """Predict and warm cache based on patterns"""
        if not self._warmup_queue:
            return
        
        # Get top candidates for warming
        warmup_candidates = list(self._warmup_queue)[:10]  # Limit to 10 items
        
        for key in warmup_candidates:
            if key not in self._cache and key not in self._lru_map:
                # This key was predicted but not in cache - could trigger preloading
                logger.debug(f"Predicted cache miss for key: {key}")
        
        self._warmup_queue.clear()
    
    def get_predictive_cache(self, key_pattern: str) -> List[str]:
        """Get predicted cache keys based on pattern"""
        predicted_keys = []
        for key, pattern in self._access_patterns.items():
            if key_pattern in key:
                # Check if this pattern is likely to be accessed soon
                current_hour = datetime.now().hour
                current_weekday = datetime.now().weekday()
                
                if (pattern.time_of_day_pattern.get(current_hour, 0) > 1 or
                    pattern.day_of_week_pattern.get(current_weekday, 0) > 0):
                    predicted_keys.append(key)
        
        return predicted_keys
    
    def warm_cache_for_category(self, category: str, limit: int = 50):
        """Warm cache with frequently accessed items from a category"""
        category_keys = []
        with self._lock:
            for key, metadata in self._cache_metadata.items():
                if metadata.get('category') == category:
                    access_count = metadata.get('access_count', 0)
                    category_keys.append((key, access_count))
        
        # Sort by access count and warm top items
        category_keys.sort(key=lambda x: x[1], reverse=True)
        for key, _ in category_keys[:limit]:
            # Pre-load into memory if using Redis
            if self._use_redis and self._redis_client:
                self._redis_client.expire(key, 300)  # Extend TTL for warm items
    
    def get_advanced_stats(self) -> Dict[str, Any]:
        """Get advanced cache statistics"""
        with self._lock:
            # Calculate memory usage
            memory_usage_mb = self._current_memory_usage / (1024 * 1024)
            memory_limit_mb = self._max_memory_bytes / (1024 * 1024)
            
            # Calculate hit rates
            total_requests = self._cache_hits + self._cache_misses
            hit_rate = self._cache_hits / total_requests if total_requests > 0 else 0
            
            # Analyze access patterns
            total_patterns = len(self._access_patterns)
            active_patterns = sum(1 for p in self._access_patterns.values() if p.access_count > 0)
            
            return {
                'memory_usage': {
                    'current_mb': round(memory_usage_mb, 2),
                    'limit_mb': round(memory_limit_mb, 2),
                    'usage_percentage': round((memory_usage_mb / memory_limit_mb) * 100, 2)
                },
                'performance': {
                    'cache_hits': self._cache_hits,
                    'cache_misses': self._cache_misses,
                    'hit_rate': round(hit_rate, 3),
                    'predictive_hits': self._predictive_cache_hits
                },
                'patterns': {
                    'total_patterns': total_patterns,
                    'active_patterns': active_patterns,
                    'prediction_queue_size': len(self._prediction_queue),
                    'warmup_queue_size': len(self._warmup_queue)
                },
                'entries': {
                    'total_entries': len(self._cache),
                    'lru_queue_size': len(self._lru_order)
                }
            }
    
    def delete(self, key: str) -> None:
        """Delete key from cache"""
        try:
            with self._lock:
                if self._use_redis and self._redis_client:
                    self._redis_client.delete(key)
                else:
                    # Update memory usage
                    if key in self._cache:
                        try:
                            value_size = len(str(self._cache[key]))
                            self._current_memory_usage -= value_size
                        except Exception:
                            self._current_memory_usage -= 1024  # Fallback
                    
                    self._cache.pop(key, None)
                    self._cache_metadata.pop(key, None)
                    
                    # Update LRU tracking
                    if key in self._lru_map:
                        self._lru_order.remove(key)
                        del self._lru_map[key]
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
    
    def clear(self) -> None:
        """Clear all cache"""
        try:
            with self._lock:
                if self._use_redis and self._redis_client:
                    self._redis_client.flushdb()
                else:
                    self._cache.clear()
                    self._cache_metadata.clear()
                    self._lru_order.clear()
                    self._lru_map.clear()
                    self._current_memory_usage = 0
                
                # Clear patterns
                self._access_patterns.clear()
                self._prediction_queue.clear()
                self._warmup_queue.clear()
        except Exception as e:
            logger.error(f"Cache clear error: {e}")

# Global advanced cache instance
advanced_cache_service = IntelligentCacheService()

def intelligent_cached(prefix: str, ttl: int = None, priority: int = 1, 
                      category: str = "default", tags: Set[str] = None):
    """Enhanced decorator for intelligent caching"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = advanced_cache_service._generate_key(prefix, *args, **kwargs)
            
            # Try to get from cache
            cached_result = advanced_cache_service.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Intelligent cache hit for {prefix}")
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            advanced_cache_service.set(
                cache_key, result, ttl, priority, category, tags or set()
            )
            logger.debug(f"Intelligent cache miss for {prefix}, stored result")
            
            return result
        return wrapper
    return decorator

class PredictiveCacheManager:
    """Manager for predictive caching operations"""
    
    @staticmethod
    def predict_and_warm_category(category: str, query_patterns: List[str]):
        """Predict and warm cache for a category based on query patterns"""
        for pattern in query_patterns:
            predicted_keys = advanced_cache_service.get_predictive_cache(pattern)
            if predicted_keys:
                logger.info(f"Predicted {len(predicted_keys)} keys for pattern: {pattern}")
                # Could trigger preloading of related documents
    
    @staticmethod
    def warm_frequently_accessed(category: str, hours_back: int = 24):
        """Warm cache with frequently accessed items from recent hours"""
        cutoff_time = datetime.now() - timedelta(hours=hours_back)
        
        # This would analyze recent access logs and warm cache
        # Implementation depends on your logging system
        logger.info(f"Warming cache for category {category} with recent access patterns")
    
    @staticmethod
    def schedule_cache_warming(category: str, schedule: Dict[str, Any]):
        """Schedule cache warming based on time patterns"""
        # This would integrate with a task scheduler
        logger.info(f"Scheduled cache warming for category {category}")

class BatchCacheOperations:
    """Batch operations for cache management"""
    
    @staticmethod
    def batch_get(keys: List[str]) -> Dict[str, Any]:
        """Get multiple cache entries in batch"""
        results = {}
        for key in keys:
            value = advanced_cache_service.get(key, update_access=False)
            if value is not None:
                results[key] = value
        return results
    
    @staticmethod
    def batch_set(entries: Dict[str, Any], ttl: int = None, 
                  priority: int = 1, category: str = "default") -> int:
        """Set multiple cache entries in batch"""
        success_count = 0
        for key, value in entries.items():
            try:
                advanced_cache_service.set(key, value, ttl, priority, category)
                success_count += 1
            except Exception as e:
                logger.error(f"Batch set error for key {key}: {e}")
        return success_count
    
    @staticmethod
    def batch_delete(keys: List[str]) -> int:
        """Delete multiple cache entries in batch"""
        success_count = 0
        for key in keys:
            try:
                advanced_cache_service.delete(key)
                success_count += 1
            except Exception as e:
                logger.error(f"Batch delete error for key {key}: {e}")
        return success_count
    
    @staticmethod
    def invalidate_by_pattern(pattern: str) -> int:
        """Invalidate cache entries matching a pattern"""
        # This would need pattern matching implementation
        # For now, return 0 as placeholder
        logger.info(f"Invalidating cache entries matching pattern: {pattern}")
        return 0 