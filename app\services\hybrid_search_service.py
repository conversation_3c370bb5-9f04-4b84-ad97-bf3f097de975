"""
Hybrid Search Service

Combines semantic vector search with keyword-based search for improved retrieval accuracy.
Implements weighted fusion of multiple search strategies.
"""

import logging
import time
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import re
from collections import Counter

from langchain.schema import Document
from langchain_ollama import OllamaEmbeddings

from app.services.unified_vector_db import get_unified_vector_db
from app.services.cache_service import cache_service
from app.services.semantic_analyzer import QueryIntent, QueryAnalysis
from app.utils.rag_performance import track_hybrid_search_metrics

logger = logging.getLogger(__name__)


class SearchStrategy(Enum):
    """Available search strategies for hybrid search."""
    SEMANTIC = "semantic"
    KEYWORD = "keyword"
    FUZZY = "fuzzy"
    EXACT_MATCH = "exact_match"
    METADATA = "metadata"


@dataclass
class SearchResult:
    """Result from a single search strategy."""
    strategy: SearchStrategy
    documents: List[Document]
    scores: List[float]
    execution_time: float
    metadata: Dict[str, Any]


@dataclass
class HybridSearchResult:
    """Combined result from hybrid search."""
    documents: List[Document]
    fusion_scores: List[float]
    strategy_contributions: Dict[SearchStrategy, float]
    execution_time: float
    search_results: List[SearchResult]
    metadata: Dict[str, Any]


class HybridSearchService:
    """
    Hybrid search service that combines multiple search strategies.
    
    Features:
    - Semantic vector search
    - Keyword-based search
    - Fuzzy matching
    - Exact match search
    - Metadata filtering
    - Weighted result fusion
    - Strategy-specific optimization
    """
    
    def __init__(self, embedding_model: str = None):
        """Initialize the hybrid search service."""
        self.embedding_model = embedding_model or "nomic-embed-text"
        self.embeddings = OllamaEmbeddings(model=self.embedding_model)
        self.vector_db = get_unified_vector_db()
        self.cache = cache_service
        
        # Strategy weights (can be adjusted based on query analysis)
        self.default_weights = {
            SearchStrategy.SEMANTIC: 0.6,
            SearchStrategy.KEYWORD: 0.25,
            SearchStrategy.FUZZY: 0.1,
            SearchStrategy.EXACT_MATCH: 0.05,
            SearchStrategy.METADATA: 0.0  # Used for filtering, not scoring
        }
        
        # Keyword extraction patterns
        self.keyword_patterns = [
            r'\b\w{3,}\b',  # Words with 3+ characters
            r'\b[A-Z][a-z]+\b',  # Proper nouns
            r'\b\d{4}\b',  # Years
            r'\b\w+[-_]\w+\b',  # Hyphenated words
        ]
        
        # Stop words (basic list, can be expanded)
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'
        }
    
    def search(self, 
               query: str, 
               category: str, 
               query_analysis: QueryAnalysis = None,
               strategies: List[SearchStrategy] = None,
               weights: Dict[SearchStrategy, float] = None,
               max_results: int = 20) -> HybridSearchResult:
        """
        Perform hybrid search using multiple strategies.
        
        Args:
            query: Search query
            category: Document category filter
            query_analysis: Optional query analysis for strategy selection
            strategies: List of search strategies to use
            weights: Custom weights for each strategy
            max_results: Maximum number of results to return
            
        Returns:
            HybridSearchResult with fused results
        """
        start_time = time.time()
        
        # Determine strategies and weights based on query analysis
        if strategies is None:
            strategies = self._select_strategies(query, query_analysis)
        
        if weights is None:
            weights = self._calculate_weights(query, query_analysis, strategies)
        
        logger.info(f"Performing hybrid search with strategies: {[s.value for s in strategies]}")
        logger.info(f"Strategy weights: {weights}")
        
        # Execute each search strategy
        search_results = []
        for strategy in strategies:
            try:
                result = self._execute_strategy(strategy, query, category, query_analysis)
                search_results.append(result)
                logger.debug(f"Strategy {strategy.value} returned {len(result.documents)} documents")
            except Exception as e:
                logger.error(f"Error executing strategy {strategy.value}: {e}")
                continue
        
        # Fuse results from all strategies
        fused_result = self._fuse_results(search_results, weights, max_results)
        
        execution_time = time.time() - start_time
        
        # Track metrics
        track_hybrid_search_metrics(
            query=query,
            category=category,
            strategies_used=[s.value for s in strategies],
            execution_time=execution_time,
            total_documents=len(fused_result.documents),
            cache_hit_rate=self._calculate_cache_hit_rate(search_results)
        )
        
        return HybridSearchResult(
            documents=fused_result['documents'],
            fusion_scores=fused_result['scores'],
            strategy_contributions=fused_result['contributions'],
            execution_time=execution_time,
            search_results=search_results,
            metadata={
                'strategies_used': [s.value for s in strategies],
                'weights_used': weights,
                'query_analysis': query_analysis.__dict__ if query_analysis else None
            }
        )
    
    def _select_strategies(self, query: str, query_analysis: QueryAnalysis = None) -> List[SearchStrategy]:
        """Select appropriate search strategies based on query analysis."""
        strategies = [SearchStrategy.SEMANTIC]  # Always include semantic search
        
        if query_analysis:
            # Add keyword search for factual queries
            if query_analysis.complexity.intent in [QueryIntent.FACTUAL, QueryIntent.ANALYTICAL]:
                strategies.append(SearchStrategy.KEYWORD)
            
            # Add exact match for specific terms
            if query_analysis.complexity.word_count < 5:
                strategies.append(SearchStrategy.EXACT_MATCH)
            
            # Add fuzzy search for complex queries
            if query_analysis.complexity.semantic_score > 0.7:
                strategies.append(SearchStrategy.FUZZY)
        else:
            # Default strategies
            strategies.extend([SearchStrategy.KEYWORD, SearchStrategy.EXACT_MATCH])
        
        return strategies
    
    def _calculate_weights(self, 
                          query: str, 
                          query_analysis: QueryAnalysis = None,
                          strategies: List[SearchStrategy] = None) -> Dict[SearchStrategy, float]:
        """Calculate weights for each search strategy."""
        weights = self.default_weights.copy()
        
        if query_analysis and strategies:
            # Adjust weights based on query complexity
            complexity = query_analysis.complexity.semantic_score
            
            if complexity > 0.8:
                # Complex queries favor semantic search
                weights[SearchStrategy.SEMANTIC] = 0.7
                weights[SearchStrategy.KEYWORD] = 0.2
                weights[SearchStrategy.FUZZY] = 0.1
            elif complexity < 0.3:
                # Simple queries favor keyword search
                weights[SearchStrategy.SEMANTIC] = 0.4
                weights[SearchStrategy.KEYWORD] = 0.5
                weights[SearchStrategy.EXACT_MATCH] = 0.1
            
            # Normalize weights for selected strategies
            total_weight = sum(weights.get(s, 0) for s in strategies)
            if total_weight > 0:
                for strategy in strategies:
                    weights[strategy] = weights.get(strategy, 0) / total_weight
        
        return weights
    
    def _execute_strategy(self, 
                         strategy: SearchStrategy, 
                         query: str, 
                         category: str,
                         query_analysis: QueryAnalysis = None) -> SearchResult:
        """Execute a single search strategy."""
        start_time = time.time()
        
        if strategy == SearchStrategy.SEMANTIC:
            return self._semantic_search(query, category, query_analysis)
        elif strategy == SearchStrategy.KEYWORD:
            return self._keyword_search(query, category, query_analysis)
        elif strategy == SearchStrategy.FUZZY:
            return self._fuzzy_search(query, category, query_analysis)
        elif strategy == SearchStrategy.EXACT_MATCH:
            return self._exact_match_search(query, category, query_analysis)
        elif strategy == SearchStrategy.METADATA:
            return self._metadata_search(query, category, query_analysis)
        else:
            raise ValueError(f"Unknown search strategy: {strategy}")
    
    def _semantic_search(self, query: str, category: str, query_analysis: QueryAnalysis = None) -> SearchResult:
        """Perform semantic vector search."""
        # Use adaptive k based on query complexity
        k = query_analysis.complexity.suggested_k if query_analysis else 12
        
        # Get embeddings for query
        query_embedding = self.embeddings.embed_query(query)
        
        # Perform similarity search
        docs_and_scores = self.vector_db.similarity_search_with_score(
            query, k=k, filter={"category": category}
        )
        
        documents = [doc for doc, _ in docs_and_scores]
        scores = [score for _, score in docs_and_scores]
        
        execution_time = time.time() - time.time()  # Will be calculated in calling method
        
        return SearchResult(
            strategy=SearchStrategy.SEMANTIC,
            documents=documents,
            scores=scores,
            execution_time=execution_time,
            metadata={'k': k, 'query_embedding_length': len(query_embedding)}
        )
    
    def _keyword_search(self, query: str, category: str, query_analysis: QueryAnalysis = None) -> SearchResult:
        """Perform keyword-based search."""
        # Extract keywords from query
        keywords = self._extract_keywords(query)
        
        if not keywords:
            return SearchResult(
                strategy=SearchStrategy.KEYWORD,
                documents=[],
                scores=[],
                execution_time=0.0,
                metadata={'keywords': []}
            )
        
        # Search for documents containing keywords
        documents = []
        scores = []
        
        # Get all documents in category (this is a simplified approach)
        # In a production system, you'd use a proper keyword index
        all_docs = self.vector_db.get(where={"category": category})
        
        for doc in all_docs['documents']:
            doc_text = doc.page_content.lower()
            keyword_matches = sum(1 for keyword in keywords if keyword.lower() in doc_text)
            
            if keyword_matches > 0:
                # Calculate keyword density score
                score = keyword_matches / len(keywords)
                documents.append(doc)
                scores.append(score)
        
        # Sort by score
        sorted_pairs = sorted(zip(documents, scores), key=lambda x: x[1], reverse=True)
        documents = [doc for doc, _ in sorted_pairs[:20]]
        scores = [score for _, score in sorted_pairs[:20]]
        
        execution_time = time.time() - time.time()  # Will be calculated in calling method
        
        return SearchResult(
            strategy=SearchStrategy.KEYWORD,
            documents=documents,
            scores=scores,
            execution_time=execution_time,
            metadata={'keywords': keywords, 'keyword_matches': len(documents)}
        )
    
    def _fuzzy_search(self, query: str, category: str, query_analysis: QueryAnalysis = None) -> SearchResult:
        """Perform fuzzy string matching search."""
        # Extract terms for fuzzy matching
        terms = query.lower().split()
        
        documents = []
        scores = []
        
        # Get all documents in category
        all_docs = self.vector_db.get(where={"category": category})
        
        for doc in all_docs['documents']:
            doc_text = doc.page_content.lower()
            doc_words = set(doc_text.split())
            
            # Calculate fuzzy match score
            matches = 0
            for term in terms:
                if len(term) >= 3:  # Only consider terms with 3+ characters
                    # Simple fuzzy matching (can be improved with proper fuzzy matching library)
                    for word in doc_words:
                        if len(word) >= 3:
                            # Check for partial matches
                            if term in word or word in term:
                                matches += 1
                                break
            
            if matches > 0:
                score = matches / len(terms)
                documents.append(doc)
                scores.append(score)
        
        # Sort by score
        sorted_pairs = sorted(zip(documents, scores), key=lambda x: x[1], reverse=True)
        documents = [doc for doc, _ in sorted_pairs[:15]]
        scores = [score for _, score in sorted_pairs[:15]]
        
        execution_time = time.time() - time.time()  # Will be calculated in calling method
        
        return SearchResult(
            strategy=SearchStrategy.FUZZY,
            documents=documents,
            scores=scores,
            execution_time=execution_time,
            metadata={'terms': terms, 'fuzzy_matches': len(documents)}
        )
    
    def _exact_match_search(self, query: str, category: str, query_analysis: QueryAnalysis = None) -> SearchResult:
        """Perform exact match search."""
        # Extract exact phrases
        phrases = self._extract_phrases(query)
        
        documents = []
        scores = []
        
        try:
            # Get all documents in category
            result = self.vector_db.get(where={"category": category})
            
            # Handle case where result might be empty or missing 'documents' key
            if not result or 'documents' not in result or not result['documents']:
                logger.warning(f"No documents found for category: {category}")
                return SearchResult(
                    strategy=SearchStrategy.EXACT_MATCH,
                    documents=[],
                    scores=[],
                    execution_time=0.0,
                    metadata={'phrases': phrases, 'exact_matches': 0}
                )
            
            all_docs = result['documents']
            
            for doc in all_docs:
                doc_text = doc.page_content.lower()
                matches = 0
                
                for phrase in phrases:
                    if phrase.lower() in doc_text:
                        matches += 1
                
                if matches > 0:
                    score = matches / len(phrases) if phrases else 0
                    documents.append(doc)
                    scores.append(score)
            
            # Sort by score
            sorted_pairs = sorted(zip(documents, scores), key=lambda x: x[1], reverse=True)
            documents = [doc for doc, _ in sorted_pairs[:10]]
            scores = [score for _, score in sorted_pairs[:10]]
            
        except Exception as e:
            logger.error(f"Error in exact match search: {str(e)}")
            documents = []
            scores = []
        
        execution_time = time.time() - time.time()  # Will be calculated in calling method
        
        return SearchResult(
            strategy=SearchStrategy.EXACT_MATCH,
            documents=documents,
            scores=scores,
            execution_time=execution_time,
            metadata={'phrases': phrases, 'exact_matches': len(documents)}
        )
    
    def _metadata_search(self, query: str, category: str, query_analysis: QueryAnalysis = None) -> SearchResult:
        """Search based on document metadata."""
        # This is a placeholder for metadata-based filtering
        # In a real implementation, you'd search metadata fields like title, author, date, etc.
        
        return SearchResult(
            strategy=SearchStrategy.METADATA,
            documents=[],
            scores=[],
            execution_time=0.0,
            metadata={'metadata_fields': []}
        )
    
    def _fuse_results(self, 
                     search_results: List[SearchResult], 
                     weights: Dict[SearchStrategy, float],
                     max_results: int) -> Dict[str, Any]:
        """Fuse results from multiple search strategies."""
        # Collect all documents with their scores
        doc_scores = {}
        strategy_contributions = {strategy: 0.0 for strategy in SearchStrategy}
        
        for result in search_results:
            strategy = result.strategy
            weight = weights.get(strategy, 0.0)
            
            for i, doc in enumerate(result.documents):
                # Create a unique key for each document
                doc_key = f"{doc.page_content[:50]}...{doc.metadata.get('source', 'unknown')}"
                
                if doc_key not in doc_scores:
                    doc_scores[doc_key] = {
                        'document': doc,
                        'scores': {},
                        'total_score': 0.0
                    }
                
                # Store individual strategy score
                score = result.scores[i] if i < len(result.scores) else 0.0
                doc_scores[doc_key]['scores'][strategy] = score
                
                # Add weighted contribution
                weighted_score = score * weight
                doc_scores[doc_key]['total_score'] += weighted_score
                strategy_contributions[strategy] += weighted_score
        
        # Sort by total score
        sorted_docs = sorted(
            doc_scores.values(), 
            key=lambda x: x['total_score'], 
            reverse=True
        )
        
        # Take top results
        final_docs = [item['document'] for item in sorted_docs[:max_results]]
        final_scores = [item['total_score'] for item in sorted_docs[:max_results]]
        
        return {
            'documents': final_docs,
            'scores': final_scores,
            'contributions': strategy_contributions
        }
    
    def _extract_keywords(self, query: str) -> List[str]:
        """Extract keywords from query."""
        keywords = []
        
        # Extract words matching patterns
        for pattern in self.keyword_patterns:
            matches = re.findall(pattern, query)
            keywords.extend(matches)
        
        # Remove stop words and duplicates
        keywords = [kw.lower() for kw in keywords if kw.lower() not in self.stop_words]
        keywords = list(set(keywords))
        
        return keywords
    
    def _extract_phrases(self, query: str) -> List[str]:
        """Extract exact phrases from query."""
        # Extract quoted phrases
        phrases = re.findall(r'"([^"]*)"', query)
        
        # Extract capitalized phrases (potential proper nouns)
        capitalized = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', query)
        phrases.extend(capitalized)
        
        return phrases
    
    def _calculate_cache_hit_rate(self, search_results: List[SearchResult]) -> float:
        """Calculate cache hit rate across all strategies."""
        # This is a placeholder - in a real implementation, you'd track cache hits
        return 0.0


def get_hybrid_search_service(embedding_model: str = None) -> HybridSearchService:
    """Get a hybrid search service instance."""
    return HybridSearchService(embedding_model)


def hybrid_search(query: str, 
                  category: str, 
                  query_analysis: QueryAnalysis = None,
                  strategies: List[SearchStrategy] = None,
                  weights: Dict[SearchStrategy, float] = None,
                  max_results: int = 20) -> HybridSearchResult:
    """
    Perform hybrid search using multiple strategies.
    
    Args:
        query: Search query
        category: Document category filter
        query_analysis: Optional query analysis for strategy selection
        strategies: List of search strategies to use
        weights: Custom weights for each strategy
        max_results: Maximum number of results to return
        
    Returns:
        HybridSearchResult with fused results
    """
    service = get_hybrid_search_service()
    return service.search(query, category, query_analysis, strategies, weights, max_results) 