"""
Enhanced Relevance Scorer Service
Provides multi-factor semantic relevance scoring for improved document ranking.
"""

import logging
import re
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import numpy as np
from langchain.schema import Document
from langchain_ollama.embeddings import OllamaEmbeddings
from app.utils.performance_monitor import performance_monitor
from app.services.semantic_analyzer import QueryIntent
from config.enhanced_retrieval_config import EnhancedScoringConfig

logger = logging.getLogger(__name__)

@dataclass
class RelevanceScore:
    """Detailed relevance scoring result"""
    overall_score: float
    semantic_score: float
    keyword_score: float
    contextual_score: float
    temporal_score: float
    domain_score: float
    confidence: float
    scoring_metadata: Dict[str, Any]

@dataclass
class ScoredDocument:
    """Document with enhanced relevance scoring"""
    document: Document
    relevance_score: RelevanceScore
    ranking_position: int
    score_breakdown: Dict[str, float]

class EnhancedRelevanceScorer:
    """
    Enhanced relevance scorer with multi-factor semantic analysis
    """
    
    def __init__(self, embedding_model: str = None, config: EnhancedScoringConfig = None):
        # Handle both string and config object inputs for backward compatibility
        if isinstance(embedding_model, EnhancedScoringConfig):
            # If embedding_model is actually a config object, extract the model name
            config = embedding_model
            embedding_model = "mxbai-embed-large:latest"  # Default model
        elif embedding_model is None:
            embedding_model = "mxbai-embed-large:latest"
        
        self.embedding_model = embedding_model
        self.config = config
        self._embedding_function = None
        
        # Use config if provided, otherwise use defaults
        if config:
            self.scoring_weights = config.scoring_weights
            self.domain_boost = config.domain_boosts
            self.temporal_decay = config.temporal_decay_rates
            self.contextual_indicators = config.contextual_indicators
        else:
            # Scoring weights (configurable)
            self.scoring_weights = {
                'semantic': 0.35,
                'keyword': 0.25,
                'contextual': 0.20,
                'temporal': 0.10,
                'domain': 0.10
            }
            
            # Domain-specific scoring boost
            self.domain_boost = {
                'forestry': 1.2,
                'environment': 1.15,
                'research': 1.1,
                'agriculture': 1.1,
                'wildlife': 1.1,
                'water': 1.1,
                'climate': 1.15
            }
            
            # Temporal relevance decay (years)
            self.temporal_decay = {
                'recent': 1.0,      # 0-2 years
                'current': 0.9,     # 3-5 years
                'moderate': 0.8,    # 6-10 years
                'older': 0.6,       # 11-20 years
                'historical': 0.4   # 20+ years
            }
            
            # Contextual relevance indicators
            self.contextual_indicators = {
                'authority': ['peer-reviewed', 'journal', 'conference', 'university', 'institute'],
                'methodology': ['method', 'methodology', 'procedure', 'technique', 'approach'],
                'completeness': ['comprehensive', 'detailed', 'complete', 'thorough', 'extensive'],
                'relevance': ['relevant', 'related', 'applicable', 'pertinent', 'appropriate']
            }
    
    def _get_embedding_function(self):
        """Get or create the embedding function"""
        if self._embedding_function is None:
            try:
                self._embedding_function = OllamaEmbeddings(model=self.embedding_model)
                logger.info(f"Initialized embedding function with model: {self.embedding_model}")
            except Exception as e:
                logger.error(f"Failed to initialize embedding function: {str(e)}")
                raise
        return self._embedding_function
    
    @performance_monitor(track_memory=True, track_cpu=True)
    def score_document_relevance(self, doc: Document, question: str, 
                                query_intent: QueryIntent = QueryIntent.UNKNOWN,
                                domain_keywords: List[str] = None) -> RelevanceScore:
        """
        Score document relevance using multiple factors
        
        Args:
            doc: Document to score
            question: User's question
            query_intent: Classified query intent
            domain_keywords: Domain-specific keywords from query
            
        Returns:
            RelevanceScore object with detailed scoring
        """
        try:
            # Calculate individual scores
            semantic_score = self._calculate_semantic_score(doc, question)
            keyword_score = self._calculate_keyword_score(doc, question)
            contextual_score = self._calculate_contextual_score(doc, question, query_intent)
            temporal_score = self._calculate_temporal_score(doc)
            domain_score = self._calculate_domain_score(doc, domain_keywords or [])
            
            # Calculate weighted overall score
            overall_score = self._calculate_weighted_score(
                semantic_score, keyword_score, contextual_score, 
                temporal_score, domain_score
            )
            
            # Calculate confidence
            confidence = self._calculate_confidence(
                semantic_score, keyword_score, contextual_score,
                temporal_score, domain_score
            )
            
            # Create scoring metadata
            scoring_metadata = {
                'scoring_timestamp': datetime.now().isoformat(),
                'embedding_model': self.embedding_model,
                'query_intent': query_intent.value if query_intent else 'unknown',
                'domain_keywords': domain_keywords or [],
                'document_length': len(doc.page_content),
                'document_source': doc.metadata.get('source', 'unknown')
            }
            
            return RelevanceScore(
                overall_score=overall_score,
                semantic_score=semantic_score,
                keyword_score=keyword_score,
                contextual_score=contextual_score,
                temporal_score=temporal_score,
                domain_score=domain_score,
                confidence=confidence,
                scoring_metadata=scoring_metadata
            )
            
        except Exception as e:
            logger.error(f"Error scoring document: {str(e)}")
            return self._create_fallback_score()
    
    def _calculate_semantic_score(self, doc: Document, question: str) -> float:
        """Calculate semantic similarity score using embeddings"""
        try:
            # Get embeddings
            embed_fn = self._get_embedding_function()
            
            # Generate embeddings for question and document
            question_embedding = embed_fn.embed_query(question)
            doc_embedding = embed_fn.embed_query(doc.page_content[:1000])  # Limit for performance
            
            # Calculate cosine similarity
            similarity = self._cosine_similarity(question_embedding, doc_embedding)
            
            # Normalize to 0-1 range
            return max(0.0, min(1.0, similarity))
            
        except Exception as e:
            logger.warning(f"Failed to calculate semantic score: {str(e)}")
            return 0.5  # Fallback score
    
    def _calculate_keyword_score(self, doc: Document, question: str) -> float:
        """Calculate keyword matching score"""
        try:
            # Extract keywords from question (remove stop words)
            stop_words = {
                'the', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about',
                'like', 'through', 'over', 'before', 'between', 'after', 'since',
                'without', 'under', 'within', 'along', 'following', 'across',
                'behind', 'beyond', 'plus', 'except', 'but', 'up', 'out', 'around',
                'down', 'off', 'above', 'near', 'and', 'or', 'is', 'are', 'was',
                'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does',
                'did', 'can', 'could', 'will', 'would', 'shall', 'should', 'may',
                'might', 'must', 'of', 'what', 'how', 'why', 'when', 'where', 'who'
            }
            
            question_words = set(question.lower().split()) - stop_words
            doc_content_lower = doc.page_content.lower()
            
            if not question_words:
                return 0.0
            
            # Count exact word matches
            exact_matches = sum(1 for word in question_words if word in doc_content_lower)
            exact_score = exact_matches / len(question_words)
            
            # Count phrase matches (2-4 word phrases)
            phrase_score = 0.0
            question_phrase = question.lower()
            for i in range(2, min(5, len(question.split()) + 1)):
                for j in range(len(question.split()) - i + 1):
                    phrase = ' '.join(question.split()[j:j+i]).lower()
                    if len(phrase) > 3 and phrase in doc_content_lower:
                        phrase_score += 0.1 * i
            
            # Combine scores
            keyword_score = min(1.0, exact_score + phrase_score)
            
            return keyword_score
            
        except Exception as e:
            logger.warning(f"Failed to calculate keyword score: {str(e)}")
            return 0.0
    
    def _calculate_contextual_score(self, doc: Document, question: str, 
                                  query_intent: QueryIntent) -> float:
        """Calculate contextual relevance score"""
        try:
            score = 0.5  # Base score
            
            # Check document metadata for contextual indicators
            metadata = doc.metadata
            
            # Authority indicators
            if any(indicator in str(metadata).lower() for indicator in self.contextual_indicators['authority']):
                score += 0.1
            
            # Methodology indicators (good for analytical queries)
            if query_intent == QueryIntent.ANALYTICAL:
                if any(indicator in doc.page_content.lower() for indicator in self.contextual_indicators['methodology']):
                    score += 0.15
            
            # Completeness indicators
            if any(indicator in doc.page_content.lower() for indicator in self.contextual_indicators['completeness']):
                score += 0.1
            
            # Relevance indicators
            if any(indicator in doc.page_content.lower() for indicator in self.contextual_indicators['relevance']):
                score += 0.05
            
            # Intent-specific scoring
            if query_intent == QueryIntent.FACTUAL:
                # Prefer documents with clear definitions
                if any(word in doc.page_content.lower() for word in ['definition', 'defined as', 'refers to']):
                    score += 0.1
            
            elif query_intent == QueryIntent.COMPARATIVE:
                # Prefer documents with comparison language
                if any(word in doc.page_content.lower() for word in ['compare', 'versus', 'difference', 'similar']):
                    score += 0.15
            
            elif query_intent == QueryIntent.QUANTITATIVE:
                # Prefer documents with numbers and statistics
                if re.search(r'\d+', doc.page_content):
                    score += 0.1
            
            return min(1.0, score)
            
        except Exception as e:
            logger.warning(f"Failed to calculate contextual score: {str(e)}")
            return 0.5
    
    def _calculate_temporal_score(self, doc: Document) -> float:
        """Calculate temporal relevance score"""
        try:
            # Get publication year from metadata
            pub_year = doc.metadata.get('published_year')
            if not pub_year:
                return 0.5  # Neutral score if no date available
            
            try:
                pub_year = int(pub_year)
                current_year = datetime.now().year
                age = current_year - pub_year
                
                # Apply temporal decay
                if age <= 2:
                    return self.temporal_decay['recent']
                elif age <= 5:
                    return self.temporal_decay['current']
                elif age <= 10:
                    return self.temporal_decay['moderate']
                elif age <= 20:
                    return self.temporal_decay['older']
                else:
                    return self.temporal_decay['historical']
                    
            except (ValueError, TypeError):
                return 0.5  # Neutral score if date parsing fails
                
        except Exception as e:
            logger.warning(f"Failed to calculate temporal score: {str(e)}")
            return 0.5
    
    def _calculate_domain_score(self, doc: Document, domain_keywords: List[str]) -> float:
        """Calculate domain relevance score"""
        try:
            if not domain_keywords:
                return 0.5  # Neutral score if no domain keywords
            
            doc_content_lower = doc.page_content.lower()
            doc_metadata_lower = str(doc.metadata).lower()
            
            # Count domain keyword matches
            matches = 0
            for keyword in domain_keywords:
                if keyword in doc_content_lower or keyword in doc_metadata_lower:
                    matches += 1
            
            # Calculate base domain score
            domain_score = matches / len(domain_keywords)
            
            # Apply domain-specific boost
            for domain, boost in self.domain_boost.items():
                if any(domain_term in doc_content_lower for domain_term in self._get_domain_terms(domain)):
                    domain_score *= boost
                    break
            
            return min(1.0, domain_score)
            
        except Exception as e:
            logger.warning(f"Failed to calculate domain score: {str(e)}")
            return 0.5
    
    def _get_domain_terms(self, domain: str) -> List[str]:
        """Get domain-specific terms"""
        domain_terms = {
            'forestry': ['forest', 'trees', 'deforestation', 'reforestation', 'timber', 'logging'],
            'environment': ['ecosystem', 'biodiversity', 'conservation', 'climate', 'pollution'],
            'research': ['study', 'analysis', 'methodology', 'findings', 'conclusion'],
            'agriculture': ['farming', 'crops', 'soil', 'irrigation', 'pesticides'],
            'wildlife': ['animals', 'species', 'habitat', 'endangered', 'conservation'],
            'water': ['water', 'aquatic', 'marine', 'freshwater', 'watershed'],
            'climate': ['climate', 'weather', 'temperature', 'precipitation', 'drought']
        }
        return domain_terms.get(domain, [])
    
    def _calculate_weighted_score(self, semantic_score: float, keyword_score: float,
                                contextual_score: float, temporal_score: float,
                                domain_score: float) -> float:
        """Calculate weighted overall score"""
        weighted_score = (
            semantic_score * self.scoring_weights['semantic'] +
            keyword_score * self.scoring_weights['keyword'] +
            contextual_score * self.scoring_weights['contextual'] +
            temporal_score * self.scoring_weights['temporal'] +
            domain_score * self.scoring_weights['domain']
        )
        
        return max(0.0, min(1.0, weighted_score))
    
    def _calculate_confidence(self, semantic_score: float, keyword_score: float,
                            contextual_score: float, temporal_score: float,
                            domain_score: float) -> float:
        """Calculate confidence in the scoring"""
        # Higher confidence when scores are consistent
        scores = [semantic_score, keyword_score, contextual_score, temporal_score, domain_score]
        variance = np.var(scores)
        
        # Lower variance = higher confidence
        confidence = max(0.1, 1.0 - variance)
        
        return confidence
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors"""
        try:
            vec1 = np.array(vec1)
            vec2 = np.array(vec2)
            
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return dot_product / (norm1 * norm2)
            
        except Exception as e:
            logger.warning(f"Failed to calculate cosine similarity: {str(e)}")
            return 0.0
    
    def _create_fallback_score(self) -> RelevanceScore:
        """Create fallback score when scoring fails"""
        return RelevanceScore(
            overall_score=0.5,
            semantic_score=0.5,
            keyword_score=0.5,
            contextual_score=0.5,
            temporal_score=0.5,
            domain_score=0.5,
            confidence=0.3,
            scoring_metadata={'fallback': True, 'error': 'Scoring failed'}
        )
    
    def score_documents_batch(self, docs: List[Document], question: str,
                            query_intent: QueryIntent = QueryIntent.UNKNOWN,
                            domain_keywords: List[str] = None) -> List[ScoredDocument]:
        """
        Score multiple documents and return ranked results
        
        Args:
            docs: List of documents to score
            question: User's question
            query_intent: Classified query intent
            domain_keywords: Domain-specific keywords
            
        Returns:
            List of ScoredDocument objects, sorted by relevance
        """
        try:
            scored_docs = []
            
            for doc in docs:
                relevance_score = self.score_document_relevance(
                    doc, question, query_intent, domain_keywords
                )
                
                scored_doc = ScoredDocument(
                    document=doc,
                    relevance_score=relevance_score,
                    ranking_position=0,  # Will be set after sorting
                    score_breakdown={
                        'semantic': relevance_score.semantic_score,
                        'keyword': relevance_score.keyword_score,
                        'contextual': relevance_score.contextual_score,
                        'temporal': relevance_score.temporal_score,
                        'domain': relevance_score.domain_score
                    }
                )
                scored_docs.append(scored_doc)
            
            # Sort by overall score (descending)
            scored_docs.sort(key=lambda x: x.relevance_score.overall_score, reverse=True)
            
            # Set ranking positions
            for i, scored_doc in enumerate(scored_docs):
                scored_doc.ranking_position = i + 1
            
            return scored_docs
            
        except Exception as e:
            logger.error(f"Error in batch scoring: {str(e)}")
            return []

# Global instance for easy access
_enhanced_scorer = None

def get_enhanced_scorer(embedding_model: str = None) -> EnhancedRelevanceScorer:
    """Get or create global enhanced scorer instance"""
    global _enhanced_scorer
    if _enhanced_scorer is None:
        _enhanced_scorer = EnhancedRelevanceScorer(embedding_model)
    return _enhanced_scorer

def score_document_enhanced(doc: Document, question: str, 
                          query_intent: QueryIntent = QueryIntent.UNKNOWN,
                          domain_keywords: List[str] = None) -> RelevanceScore:
    """Convenience function for enhanced document scoring"""
    scorer = get_enhanced_scorer()
    return scorer.score_document_relevance(doc, question, query_intent, domain_keywords)

def score_documents_enhanced_batch(docs: List[Document], question: str,
                                 query_intent: QueryIntent = QueryIntent.UNKNOWN,
                                 domain_keywords: List[str] = None) -> List[ScoredDocument]:
    """Convenience function for batch enhanced document scoring"""
    scorer = get_enhanced_scorer()
    return scorer.score_documents_batch(docs, question, query_intent, domain_keywords) 