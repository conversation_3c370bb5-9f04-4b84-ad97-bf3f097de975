"""
Result Diversification and Ranking Service

This module provides sophisticated result diversification, ranking, and presentation
capabilities to improve the quality and diversity of search results.
"""

import logging
from typing import List, Dict, Set, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
from collections import defaultdict, Counter
import math
import re

from langchain.schema import Document
from app.services.enhanced_relevance_scorer import EnhancedRelevanceScorer, ScoredDocument
from app.services.semantic_analyzer import SemanticAnalyzer
from app.utils.rag_performance import track_result_diversification_metrics
from config.enhanced_retrieval_config import EnhancedRetrievalConfig

logger = logging.getLogger(__name__)


@dataclass
class DiversificationConfig:
    """Configuration for result diversification"""
    diversity_threshold: float = 0.3
    max_similar_documents: int = 3
    coverage_weight: float = 0.4
    relevance_weight: float = 0.4
    diversity_weight: float = 0.2
    novelty_weight: float = 0.1
    enable_temporal_diversity: bool = True
    enable_domain_diversity: bool = True
    enable_perspective_diversity: bool = True


@dataclass
class DiversifiedResult:
    """Represents a diversified search result"""
    documents: List[Document]
    diversity_score: float
    coverage_score: float
    relevance_distribution: Dict[str, float]
    diversity_metrics: Dict[str, float]
    ranking_strategy: str
    timestamp: datetime


@dataclass
class RankingStrategy:
    """Represents a ranking strategy configuration"""
    name: str
    weights: Dict[str, float]
    diversity_threshold: float
    max_results: int
    enable_reranking: bool


class ResultDiversifier:
    """
    Advanced result diversification and ranking service
    
    Provides:
    - Intelligent result diversification
    - Multi-factor ranking strategies
    - Coverage optimization
    - Novelty detection
    - Temporal and domain diversity
    - Perspective diversity
    - Adaptive ranking based on query type
    """
    
    def __init__(self, config: EnhancedRetrievalConfig = None):
        self.config = config or EnhancedRetrievalConfig()
        self.relevance_scorer = EnhancedRelevanceScorer(self.config.enhanced_scoring)
        self.semantic_analyzer = SemanticAnalyzer(self.config.semantic_analyzer)
        
        # Diversification strategies
        self.diversification_strategies = {
            'maximal_marginal_relevance': self._maximal_marginal_relevance,
            'coverage_optimization': self._coverage_optimization,
            'novelty_ranking': self._novelty_ranking,
            'temporal_diversity': self._temporal_diversity,
            'domain_diversity': self._domain_diversity,
            'perspective_diversity': self._perspective_diversity
        }
        
        # Ranking strategies
        self.ranking_strategies = {
            'balanced': RankingStrategy(
                name='balanced',
                weights={'relevance': 0.5, 'diversity': 0.3, 'coverage': 0.2},
                diversity_threshold=0.3,
                max_results=20,
                enable_reranking=True
            ),
            'relevance_focused': RankingStrategy(
                name='relevance_focused',
                weights={'relevance': 0.8, 'diversity': 0.1, 'coverage': 0.1},
                diversity_threshold=0.2,
                max_results=15,
                enable_reranking=False
            ),
            'diversity_focused': RankingStrategy(
                name='diversity_focused',
                weights={'relevance': 0.3, 'diversity': 0.5, 'coverage': 0.2},
                diversity_threshold=0.5,
                max_results=25,
                enable_reranking=True
            ),
            'coverage_focused': RankingStrategy(
                name='coverage_focused',
                weights={'relevance': 0.4, 'diversity': 0.2, 'coverage': 0.4},
                diversity_threshold=0.4,
                max_results=30,
                enable_reranking=True
            )
        }
    
    def diversify_results(self, documents: List[Document], query: str, 
                         strategy: str = 'balanced', max_results: int = None) -> DiversifiedResult:
        """
        Diversify and rank search results
        
        Args:
            documents: List of documents to diversify
            query: The original query
            strategy: Ranking strategy ('balanced', 'relevance_focused', 'diversity_focused', 'coverage_focused')
            max_results: Maximum number of results to return
            
        Returns:
            DiversifiedResult object with diversified documents
        """
        start_time = datetime.now()
        
        try:
            if not documents:
                return self._create_empty_result()
            
            # Get ranking strategy
            ranking_strategy = self.ranking_strategies.get(strategy, self.ranking_strategies['balanced'])
            
            # Set max_results if not provided
            if max_results is None:
                max_results = ranking_strategy.max_results
            
            # Score documents for relevance
            scored_docs = self.relevance_scorer.score_documents_batch(documents, query)
            
            # Apply diversification
            diversified_docs = self._apply_diversification(
                scored_docs, query, ranking_strategy, max_results
            )
            
            # Calculate diversity metrics
            diversity_metrics = self._calculate_diversity_metrics(diversified_docs)
            
            # Calculate coverage score
            coverage_score = self._calculate_coverage_score(diversified_docs, query)
            
            # Calculate relevance distribution
            relevance_distribution = self._calculate_relevance_distribution(diversified_docs)
            
            # Calculate overall diversity score
            diversity_score = self._calculate_overall_diversity_score(
                diversity_metrics, coverage_score, relevance_distribution
            )
            
            result = DiversifiedResult(
                documents=[doc.document for doc in diversified_docs],
                diversity_score=diversity_score,
                coverage_score=coverage_score,
                relevance_distribution=relevance_distribution,
                diversity_metrics=diversity_metrics,
                ranking_strategy=strategy,
                timestamp=datetime.now()
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            track_result_diversification_metrics('diversification', execution_time, len(diversified_docs))
            
            logger.info(f"Result diversification completed in {execution_time:.3f}s")
            logger.info(f"Strategy: {strategy}, Diversity score: {diversity_score:.3f}")
            logger.info(f"Coverage score: {coverage_score:.3f}, Documents: {len(diversified_docs)}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in result diversification: {e}")
            execution_time = (datetime.now() - start_time).total_seconds()
            track_result_diversification_metrics('diversification_error', execution_time, 0, error_message=str(e))
            raise
    
    def _apply_diversification(self, scored_docs: List[ScoredDocument], query: str,
                             ranking_strategy: RankingStrategy, max_results: int) -> List[ScoredDocument]:
        """Apply diversification strategies to scored documents"""
        # Start with top relevance documents
        sorted_docs = sorted(scored_docs, key=lambda x: x.relevance_score.overall_score, reverse=True)
        
        # Apply maximal marginal relevance for initial diversification
        diversified_docs = self._maximal_marginal_relevance(
            sorted_docs, query, ranking_strategy.diversity_threshold, max_results
        )
        
        # Apply additional diversification strategies if enabled
        if ranking_strategy.enable_reranking:
            # Coverage optimization
            diversified_docs = self._coverage_optimization(diversified_docs, query, max_results)
            
            # Temporal diversity
            if self.config.result_diversification.enable_temporal_diversity:
                diversified_docs = self._temporal_diversity(diversified_docs, max_results)
            
            # Domain diversity
            if self.config.result_diversification.enable_domain_diversity:
                diversified_docs = self._domain_diversity(diversified_docs, max_results)
            
            # Perspective diversity
            if self.config.result_diversification.enable_perspective_diversity:
                diversified_docs = self._perspective_diversity(diversified_docs, max_results)
        
        # Final ranking with strategy weights
        final_docs = self._apply_ranking_strategy(
            diversified_docs, query, ranking_strategy, max_results
        )
        
        return final_docs
    
    def _maximal_marginal_relevance(self, documents: List[ScoredDocument], query: str,
                                   diversity_threshold: float, max_results: int) -> List[ScoredDocument]:
        """Apply Maximal Marginal Relevance (MMR) for diversity"""
        if not documents:
            return []
        
        # Start with the most relevant document
        selected = [documents[0]]
        remaining = documents[1:]
        
        while len(selected) < max_results and remaining:
            # Calculate MMR scores
            mmr_scores = []
            for doc in remaining:
                # Relevance to query
                relevance = doc.relevance_score.overall_score
                
                # Maximum similarity to already selected documents
                max_similarity = 0.0
                for selected_doc in selected:
                    similarity = self._calculate_document_similarity(doc.document, selected_doc.document)
                    max_similarity = max(max_similarity, similarity)
                
                # MMR score = λ * relevance - (1-λ) * max_similarity
                lambda_param = 0.7  # Balance between relevance and diversity
                mmr_score = lambda_param * relevance - (1 - lambda_param) * max_similarity
                mmr_scores.append((doc, mmr_score))
            
            # Select document with highest MMR score
            if mmr_scores:
                best_doc, _ = max(mmr_scores, key=lambda x: x[1])
                selected.append(best_doc)
                remaining.remove(best_doc)
            else:
                break
        
        return selected
    
    def _coverage_optimization(self, documents: List[ScoredDocument], query: str, 
                             max_results: int) -> List[ScoredDocument]:
        """Optimize for query coverage"""
        if not documents:
            return []
        
        # Analyze query to identify key concepts
        query_analysis = self.semantic_analyzer.analyze_query(query)
        key_concepts = set()
        
        # Add domain keywords
        if query_analysis.domain_keywords:
            key_concepts.update(query_analysis.domain_keywords)
        
        # Add entities from query
        words = query.lower().split()
        key_concepts.update(words)
        
        # Score documents based on concept coverage
        coverage_scores = []
        for doc in documents:
            doc_text = doc.document.page_content.lower()
            covered_concepts = sum(1 for concept in key_concepts if concept in doc_text)
            coverage_ratio = covered_concepts / len(key_concepts) if key_concepts else 0.0
            coverage_scores.append((doc, coverage_ratio))
        
        # Sort by coverage and relevance combination
        coverage_scores.sort(key=lambda x: x[1] * 0.6 + x[0].relevance_score.overall_score * 0.4, reverse=True)
        
        return [doc for doc, _ in coverage_scores[:max_results]]
    
    def _novelty_ranking(self, documents: List[ScoredDocument], max_results: int) -> List[ScoredDocument]:
        """Rank documents by novelty (information not covered by previous documents)"""
        if not documents:
            return []
        
        # Extract key phrases from documents
        document_phrases = []
        for doc in documents:
            phrases = self._extract_key_phrases(doc.document.page_content)
            document_phrases.append((doc, phrases))
        
        # Calculate novelty scores
        novelty_scores = []
        covered_phrases = set()
        
        for doc, phrases in document_phrases:
            # Count novel phrases
            novel_phrases = phrases - covered_phrases
            novelty_ratio = len(novel_phrases) / len(phrases) if phrases else 0.0
            
            # Combine with relevance score
            novelty_score = novelty_ratio * 0.7 + doc.relevance_score.overall_score * 0.3
            novelty_scores.append((doc, novelty_score))
            
            # Update covered phrases
            covered_phrases.update(phrases)
        
        # Sort by novelty score
        novelty_scores.sort(key=lambda x: x[1], reverse=True)
        
        return [doc for doc, _ in novelty_scores[:max_results]]
    
    def _temporal_diversity(self, documents: List[ScoredDocument], max_results: int) -> List[ScoredDocument]:
        """Ensure temporal diversity in results"""
        if not documents:
            return []
        
        # Extract temporal information from documents
        temporal_groups = defaultdict(list)
        
        for doc in documents:
            temporal_info = self._extract_temporal_info(doc.document.page_content)
            if temporal_info:
                temporal_groups[temporal_info].append(doc)
            else:
                temporal_groups['unknown'].append(doc)
        
        # Select documents from different temporal periods
        diversified = []
        max_per_group = max(1, max_results // len(temporal_groups))
        
        for group, docs in temporal_groups.items():
            # Sort by relevance within each group
            sorted_docs = sorted(docs, key=lambda x: x.relevance_score.overall_score, reverse=True)
            diversified.extend(sorted_docs[:max_per_group])
        
        # Sort by relevance and limit to max_results
        diversified.sort(key=lambda x: x.relevance_score.overall_score, reverse=True)
        return diversified[:max_results]
    
    def _domain_diversity(self, documents: List[ScoredDocument], max_results: int) -> List[ScoredDocument]:
        """Ensure domain diversity in results"""
        if not documents:
            return []
        
        # Extract domain information from documents
        domain_groups = defaultdict(list)
        
        for doc in documents:
            domain_info = self._extract_domain_info(doc.document.page_content)
            domain_groups[domain_info].append(doc)
        
        # Select documents from different domains
        diversified = []
        max_per_domain = max(1, max_results // len(domain_groups))
        
        for domain, docs in domain_groups.items():
            # Sort by relevance within each domain
            sorted_docs = sorted(docs, key=lambda x: x.relevance_score.overall_score, reverse=True)
            diversified.extend(sorted_docs[:max_per_domain])
        
        # Sort by relevance and limit to max_results
        diversified.sort(key=lambda x: x.relevance_score.overall_score, reverse=True)
        return diversified[:max_results]
    
    def _perspective_diversity(self, documents: List[ScoredDocument], max_results: int) -> List[ScoredDocument]:
        """Ensure perspective diversity in results"""
        if not documents:
            return []
        
        # Extract perspective indicators from documents
        perspective_groups = defaultdict(list)
        
        for doc in documents:
            perspective = self._extract_perspective_info(doc.document.page_content)
            perspective_groups[perspective].append(doc)
        
        # Select documents from different perspectives
        diversified = []
        max_per_perspective = max(1, max_results // len(perspective_groups))
        
        for perspective, docs in perspective_groups.items():
            # Sort by relevance within each perspective
            sorted_docs = sorted(docs, key=lambda x: x.relevance_score.overall_score, reverse=True)
            diversified.extend(sorted_docs[:max_per_perspective])
        
        # Sort by relevance and limit to max_results
        diversified.sort(key=lambda x: x.relevance_score.overall_score, reverse=True)
        return diversified[:max_results]
    
    def _apply_ranking_strategy(self, documents: List[ScoredDocument], query: str,
                               ranking_strategy: RankingStrategy, max_results: int) -> List[ScoredDocument]:
        """Apply final ranking strategy with weights"""
        if not documents:
            return []
        
        # Calculate final scores using strategy weights
        final_scores = []
        for doc in documents:
            # Relevance score (normalized)
            relevance_score = doc.relevance_score.overall_score
            
            # Diversity score (based on similarity to other documents)
            diversity_score = self._calculate_diversity_score(doc, documents)
            
            # Coverage score (based on query coverage)
            coverage_score = self._calculate_coverage_score([doc], query)
            
            # Weighted final score
            final_score = (
                ranking_strategy.weights.get('relevance', 0.5) * relevance_score +
                ranking_strategy.weights.get('diversity', 0.3) * diversity_score +
                ranking_strategy.weights.get('coverage', 0.2) * coverage_score
            )
            
            final_scores.append((doc, final_score))
        
        # Sort by final score
        final_scores.sort(key=lambda x: x[1], reverse=True)
        
        return [doc for doc, _ in final_scores[:max_results]]
    
    def _calculate_document_similarity(self, doc1: Document, doc2: Document) -> float:
        """Calculate similarity between two documents"""
        # Simple text similarity based on common words
        words1 = set(doc1.page_content.lower().split())
        words2 = set(doc2.page_content.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _extract_key_phrases(self, text: str) -> Set[str]:
        """Extract key phrases from text"""
        # Simple phrase extraction (can be enhanced with NLP)
        words = text.lower().split()
        phrases = set()
        
        # Extract 2-3 word phrases
        for i in range(len(words) - 1):
            phrases.add(f"{words[i]} {words[i+1]}")
        
        for i in range(len(words) - 2):
            phrases.add(f"{words[i]} {words[i+1]} {words[i+2]}")
        
        return phrases
    
    def _extract_temporal_info(self, text: str) -> str:
        """Extract temporal information from text"""
        # Simple temporal extraction
        temporal_patterns = [
            r'\b\d{4}\b',  # Year
            r'\b(recent|current|latest|new|old|historical|future)\b',
            r'\b(last|next|previous|upcoming)\s+(year|month|week|day)\b'
        ]
        
        for pattern in temporal_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(0)
        
        return 'unknown'
    
    def _extract_domain_info(self, text: str) -> str:
        """Extract domain information from text"""
        # Simple domain detection
        domain_keywords = {
            'forestry': ['tree', 'forest', 'timber', 'harvest', 'sustainability'],
            'agriculture': ['crop', 'soil', 'irrigation', 'pest', 'fertilizer'],
            'environmental': ['pollution', 'climate', 'conservation', 'ecosystem']
        }
        
        text_lower = text.lower()
        domain_scores = defaultdict(int)
        
        for domain, keywords in domain_keywords.items():
            for keyword in keywords:
                if keyword in text_lower:
                    domain_scores[domain] += 1
        
        if domain_scores:
            return max(domain_scores, key=domain_scores.get)
        
        return 'general'
    
    def _extract_perspective_info(self, text: str) -> str:
        """Extract perspective information from text"""
        # Simple perspective detection
        perspective_indicators = {
            'technical': ['method', 'procedure', 'technique', 'analysis', 'data'],
            'practical': ['application', 'implementation', 'practice', 'guidance'],
            'theoretical': ['theory', 'concept', 'principle', 'framework', 'model'],
            'research': ['study', 'research', 'investigation', 'experiment', 'finding']
        }
        
        text_lower = text.lower()
        perspective_scores = defaultdict(int)
        
        for perspective, indicators in perspective_indicators.items():
            for indicator in indicators:
                if indicator in text_lower:
                    perspective_scores[perspective] += 1
        
        if perspective_scores:
            return max(perspective_scores, key=perspective_scores.get)
        
        return 'general'
    
    def _calculate_diversity_score(self, doc: ScoredDocument, all_docs: List[ScoredDocument]) -> float:
        """Calculate diversity score for a document"""
        if len(all_docs) <= 1:
            return 1.0
        
        # Calculate average similarity to other documents
        similarities = []
        for other_doc in all_docs:
            if other_doc != doc:
                similarity = self._calculate_document_similarity(doc.document, other_doc.document)
                similarities.append(similarity)
        
        if not similarities:
            return 1.0
        
        # Diversity score is inverse of average similarity
        avg_similarity = sum(similarities) / len(similarities)
        return 1.0 - avg_similarity
    
    def _calculate_coverage_score(self, documents: List[ScoredDocument], query: str) -> float:
        """Calculate coverage score for documents"""
        if not documents:
            return 0.0
        
        # Simple coverage based on query terms
        query_terms = set(query.lower().split())
        if not query_terms:
            return 0.0
        
        covered_terms = set()
        for doc in documents:
            doc_text = doc.document.page_content.lower()
            for term in query_terms:
                if term in doc_text:
                    covered_terms.add(term)
        
        return len(covered_terms) / len(query_terms) if query_terms else 0.0
    
    def _calculate_relevance_distribution(self, documents: List[ScoredDocument]) -> Dict[str, float]:
        """Calculate relevance score distribution"""
        if not documents:
            return {}
        
        scores = [doc.relevance_score.overall_score for doc in documents]
        
        return {
            'mean': sum(scores) / len(scores),
            'min': min(scores),
            'max': max(scores),
            'std': self._calculate_std(scores),
            'high_relevance_ratio': sum(1 for s in scores if s > 0.7) / len(scores)
        }
    
    def _calculate_std(self, values: List[float]) -> float:
        """Calculate standard deviation"""
        if len(values) <= 1:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return math.sqrt(variance)
    
    def _calculate_diversity_metrics(self, documents: List[ScoredDocument]) -> Dict[str, float]:
        """Calculate comprehensive diversity metrics"""
        if not documents:
            return {}
        
        # Calculate pairwise similarities
        similarities = []
        for i, doc1 in enumerate(documents):
            for j, doc2 in enumerate(documents[i+1:], i+1):
                similarity = self._calculate_document_similarity(doc1.document, doc2.document)
                similarities.append(similarity)
        
        if not similarities:
            return {'average_similarity': 0.0, 'diversity_score': 1.0}
        
        avg_similarity = sum(similarities) / len(similarities)
        diversity_score = 1.0 - avg_similarity
        
        return {
            'average_similarity': avg_similarity,
            'diversity_score': diversity_score,
            'similarity_std': self._calculate_std(similarities),
            'unique_documents_ratio': len(set(doc.document.page_content for doc in documents)) / len(documents)
        }
    
    def _calculate_overall_diversity_score(self, diversity_metrics: Dict[str, float],
                                         coverage_score: float,
                                         relevance_distribution: Dict[str, float]) -> float:
        """Calculate overall diversity score"""
        # Combine various metrics
        diversity_score = diversity_metrics.get('diversity_score', 0.0)
        coverage_weight = 0.3
        diversity_weight = 0.4
        relevance_weight = 0.3
        
        # Normalize relevance distribution contribution
        relevance_contribution = relevance_distribution.get('high_relevance_ratio', 0.0)
        
        overall_score = (
            diversity_weight * diversity_score +
            coverage_weight * coverage_score +
            relevance_weight * relevance_contribution
        )
        
        return min(overall_score, 1.0)
    
    def _create_empty_result(self) -> DiversifiedResult:
        """Create empty result when no documents are provided"""
        return DiversifiedResult(
            documents=[],
            diversity_score=0.0,
            coverage_score=0.0,
            relevance_distribution={},
            diversity_metrics={},
            ranking_strategy='none',
            timestamp=datetime.now()
        )


# Global instance
result_diversifier = ResultDiversifier()


def diversify_results(documents: List[Document], query: str, 
                     strategy: str = 'balanced', max_results: int = None) -> DiversifiedResult:
    """Convenience function for result diversification"""
    return result_diversifier.diversify_results(documents, query, strategy, max_results) 