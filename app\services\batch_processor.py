"""
Batch Processing Service for ERDB Document Management System
Implements parallel document processing, batch embeddings, and optimized bulk operations
"""

import asyncio
import time
import logging
from typing import List, Dict, Any, Optional, Callable, Tuple
from dataclasses import dataclass, asdict
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, ProcessPoolExecutor, as_completed
from functools import partial
import threading
from collections import defaultdict
import queue
import json

logger = logging.getLogger(__name__)

@dataclass
class BatchJob:
    """Represents a batch processing job"""
    job_id: str
    job_type: str  # 'embedding', 'document_processing', 'vector_search', 'cache_warming'
    items: List[Any]
    priority: int = 1
    max_workers: int = 4
    timeout: int = 300  # 5 minutes
    created: float = None
    status: str = 'pending'  # pending, running, completed, failed
    results: List[Any] = None
    errors: List[str] = None
    
    def __post_init__(self):
        if self.created is None:
            self.created = time.time()
        if self.results is None:
            self.results = []
        if self.errors is None:
            self.errors = []

@dataclass
class BatchResult:
    """Result of a batch operation"""
    job_id: str
    total_items: int
    successful_items: int
    failed_items: int
    execution_time: float
    throughput: float  # items per second
    errors: List[str]
    results: List[Any]

class BatchProcessor:
    """Advanced batch processing service with parallel execution"""
    
    def __init__(self, max_workers: int = 8, max_queue_size: int = 1000):
        self.max_workers = max_workers
        self.max_queue_size = max_queue_size
        self.job_queue = queue.PriorityQueue(maxsize=max_queue_size)
        self.active_jobs: Dict[str, BatchJob] = {}
        self.completed_jobs: Dict[str, BatchResult] = {}
        self.job_counter = 0
        self.lock = threading.RLock()
        
        # Thread pools
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        self.process_pool = ProcessPoolExecutor(max_workers=max_workers//2)  # Use fewer processes
        
        # Performance tracking
        self.total_jobs_processed = 0
        self.total_items_processed = 0
        self.total_execution_time = 0.0
        
        # Start worker threads
        self._start_workers()
        logger.info(f"Batch processor initialized with {max_workers} workers")
    
    def _start_workers(self):
        """Start worker threads for processing jobs"""
        def worker():
            while True:
                try:
                    # Get job from queue (priority-based)
                    priority, job = self.job_queue.get(timeout=1)
                    if job is None:  # Shutdown signal
                        break
                    
                    self._process_job(job)
                    self.job_queue.task_done()
                    
                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"Worker thread error: {e}")
        
        # Start worker threads
        for i in range(self.max_workers):
            worker_thread = threading.Thread(target=worker, daemon=True)
            worker_thread.start()
    
    def _process_job(self, job: BatchJob):
        """Process a batch job"""
        try:
            with self.lock:
                job.status = 'running'
                self.active_jobs[job.job_id] = job
            
            start_time = time.time()
            
            # Process based on job type
            if job.job_type == 'embedding':
                results = self._process_embeddings_batch(job)
            elif job.job_type == 'document_processing':
                results = self._process_documents_batch(job)
            elif job.job_type == 'vector_search':
                results = self._process_vector_search_batch(job)
            elif job.job_type == 'cache_warming':
                results = self._process_cache_warming_batch(job)
            else:
                raise ValueError(f"Unknown job type: {job.job_type}")
            
            execution_time = time.time() - start_time
            
            # Create batch result
            successful_items = len([r for r in results if r is not None])
            failed_items = len(job.items) - successful_items
            throughput = len(job.items) / execution_time if execution_time > 0 else 0
            
            batch_result = BatchResult(
                job_id=job.job_id,
                total_items=len(job.items),
                successful_items=successful_items,
                failed_items=failed_items,
                execution_time=execution_time,
                throughput=throughput,
                errors=job.errors,
                results=results
            )
            
            # Update tracking
            with self.lock:
                self.completed_jobs[job.job_id] = batch_result
                self.total_jobs_processed += 1
                self.total_items_processed += len(job.items)
                self.total_execution_time += execution_time
                
                if job.job_id in self.active_jobs:
                    del self.active_jobs[job.job_id]
            
            logger.info(f"Batch job {job.job_id} completed: {successful_items}/{len(job.items)} items in {execution_time:.2f}s")
            
        except Exception as e:
            logger.error(f"Error processing batch job {job.job_id}: {e}")
            job.status = 'failed'
            job.errors.append(str(e))
    
    def _process_embeddings_batch(self, job: BatchJob) -> List[Any]:
        """Process batch embeddings using parallel execution"""
        def process_embedding(text: str) -> Optional[List[float]]:
            try:
                # This would integrate with your actual embedding service
                # For now, return a mock embedding
                return [0.1] * 768  # Mock 768-dimensional embedding
            except Exception as e:
                logger.error(f"Embedding error for text: {e}")
                return None
        
        # Use thread pool for I/O-bound embedding operations
        futures = []
        for text in job.items:
            future = self.thread_pool.submit(process_embedding, text)
            futures.append(future)
        
        # Collect results
        results = []
        for future in as_completed(futures, timeout=job.timeout):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                job.errors.append(str(e))
                results.append(None)
        
        return results
    
    def _process_documents_batch(self, job: BatchJob) -> List[Any]:
        """Process batch document operations"""
        def process_document(doc_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
            try:
                # This would integrate with your actual document processing
                # For now, return processed document
                return {
                    'id': doc_data.get('id'),
                    'processed': True,
                    'timestamp': time.time()
                }
            except Exception as e:
                logger.error(f"Document processing error: {e}")
                return None
        
        # Use thread pool for document processing
        futures = []
        for doc in job.items:
            future = self.thread_pool.submit(process_document, doc)
            futures.append(future)
        
        # Collect results
        results = []
        for future in as_completed(futures, timeout=job.timeout):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                job.errors.append(str(e))
                results.append(None)
        
        return results
    
    def _process_vector_search_batch(self, job: BatchJob) -> List[Any]:
        """Process batch vector searches"""
        def process_search(search_params: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
            try:
                # This would integrate with your actual vector search
                # For now, return mock search results
                return [
                    {'id': f'doc_{i}', 'score': 0.9 - i * 0.1}
                    for i in range(5)
                ]
            except Exception as e:
                logger.error(f"Vector search error: {e}")
                return None
        
        # Use thread pool for vector searches
        futures = []
        for params in job.items:
            future = self.thread_pool.submit(process_search, params)
            futures.append(future)
        
        # Collect results
        results = []
        for future in as_completed(futures, timeout=job.timeout):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                job.errors.append(str(e))
                results.append(None)
        
        return results
    
    def _process_cache_warming_batch(self, job: BatchJob) -> List[Any]:
        """Process batch cache warming operations"""
        def warm_cache_item(cache_key: str) -> Optional[bool]:
            try:
                # This would integrate with your actual cache warming
                # For now, return success
                return True
            except Exception as e:
                logger.error(f"Cache warming error for key {cache_key}: {e}")
                return None
        
        # Use thread pool for cache warming
        futures = []
        for cache_key in job.items:
            future = self.thread_pool.submit(warm_cache_item, cache_key)
            futures.append(future)
        
        # Collect results
        results = []
        for future in as_completed(futures, timeout=job.timeout):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                job.errors.append(str(e))
                results.append(None)
        
        return results
    
    def submit_job(self, job_type: str, items: List[Any], priority: int = 1, 
                   max_workers: int = None, timeout: int = 300) -> str:
        """Submit a batch job for processing"""
        with self.lock:
            self.job_counter += 1
            job_id = f"batch_{self.job_counter}_{int(time.time())}"
        
        job = BatchJob(
            job_id=job_id,
            job_type=job_type,
            items=items,
            priority=priority,
            max_workers=max_workers or self.max_workers,
            timeout=timeout
        )
        
        # Add to priority queue (lower priority number = higher priority)
        self.job_queue.put((priority, job))
        
        logger.info(f"Submitted batch job {job_id}: {job_type} with {len(items)} items")
        return job_id
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a batch job"""
        with self.lock:
            # Check active jobs
            if job_id in self.active_jobs:
                job = self.active_jobs[job_id]
                return {
                    'job_id': job_id,
                    'status': job.status,
                    'progress': 0,  # Could track progress
                    'created': job.created,
                    'items_count': len(job.items)
                }
            
            # Check completed jobs
            if job_id in self.completed_jobs:
                result = self.completed_jobs[job_id]
                return {
                    'job_id': job_id,
                    'status': 'completed',
                    'total_items': result.total_items,
                    'successful_items': result.successful_items,
                    'failed_items': result.failed_items,
                    'execution_time': result.execution_time,
                    'throughput': result.throughput,
                    'errors': result.errors
                }
        
        return None
    
    def get_job_result(self, job_id: str) -> Optional[BatchResult]:
        """Get result of a completed batch job"""
        with self.lock:
            return self.completed_jobs.get(job_id)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get batch processing performance statistics"""
        with self.lock:
            avg_execution_time = (self.total_execution_time / self.total_jobs_processed 
                                if self.total_jobs_processed > 0 else 0)
            avg_throughput = (self.total_items_processed / self.total_execution_time 
                            if self.total_execution_time > 0 else 0)
            
            return {
                'total_jobs_processed': self.total_jobs_processed,
                'total_items_processed': self.total_items_processed,
                'total_execution_time': self.total_execution_time,
                'avg_execution_time': avg_execution_time,
                'avg_throughput': avg_throughput,
                'active_jobs': len(self.active_jobs),
                'queued_jobs': self.job_queue.qsize(),
                'completed_jobs': len(self.completed_jobs)
            }
    
    def shutdown(self):
        """Shutdown the batch processor"""
        logger.info("Shutting down batch processor...")
        
        # Stop accepting new jobs
        for _ in range(self.max_workers):
            self.job_queue.put((0, None))  # Shutdown signal
        
        # Wait for active jobs to complete
        self.job_queue.join()
        
        # Shutdown thread pools
        self.thread_pool.shutdown(wait=True)
        self.process_pool.shutdown(wait=True)
        
        logger.info("Batch processor shutdown complete")

class DocumentBatchProcessor:
    """Specialized batch processor for document operations"""
    
    def __init__(self, batch_processor: BatchProcessor):
        self.batch_processor = batch_processor
    
    def batch_extract_text(self, pdf_paths: List[str]) -> str:
        """Extract text from multiple PDFs in batch"""
        job_id = self.batch_processor.submit_job(
            job_type='document_processing',
            items=[{'type': 'pdf_extract', 'path': path} for path in pdf_paths],
            priority=1
        )
        return job_id
    
    def batch_generate_embeddings(self, texts: List[str]) -> str:
        """Generate embeddings for multiple texts in batch"""
        job_id = self.batch_processor.submit_job(
            job_type='embedding',
            items=texts,
            priority=2
        )
        return job_id
    
    def batch_vector_search(self, queries: List[Dict[str, Any]]) -> str:
        """Perform vector search for multiple queries in batch"""
        job_id = self.batch_processor.submit_job(
            job_type='vector_search',
            items=queries,
            priority=3
        )
        return job_id

class CacheBatchProcessor:
    """Specialized batch processor for cache operations"""
    
    def __init__(self, batch_processor: BatchProcessor):
        self.batch_processor = batch_processor
    
    def batch_warm_cache(self, cache_keys: List[str]) -> str:
        """Warm cache for multiple keys in batch"""
        job_id = self.batch_processor.submit_job(
            job_type='cache_warming',
            items=cache_keys,
            priority=4
        )
        return job_id
    
    def batch_invalidate_cache(self, patterns: List[str]) -> str:
        """Invalidate cache for multiple patterns in batch"""
        # This would be implemented as a cache warming job with invalidation
        job_id = self.batch_processor.submit_job(
            job_type='cache_warming',
            items=patterns,
            priority=5
        )
        return job_id

# Global batch processor instance
_batch_processor: Optional[BatchProcessor] = None

def get_batch_processor() -> BatchProcessor:
    """Get the global batch processor instance"""
    global _batch_processor
    if _batch_processor is None:
        _batch_processor = BatchProcessor()
    return _batch_processor

def get_document_batch_processor() -> DocumentBatchProcessor:
    """Get the global document batch processor instance"""
    return DocumentBatchProcessor(get_batch_processor())

def get_cache_batch_processor() -> CacheBatchProcessor:
    """Get the global cache batch processor instance"""
    return CacheBatchProcessor(get_batch_processor())

# Utility functions for common batch operations
def batch_process_documents(documents: List[Dict[str, Any]], 
                           processor_func: Callable) -> List[Any]:
    """Process documents in batch using the specified processor function"""
    batch_processor = get_batch_processor()
    
    # Submit job
    job_id = batch_processor.submit_job(
        job_type='document_processing',
        items=documents,
        priority=1
    )
    
    # Wait for completion (in a real implementation, this would be async)
    while True:
        status = batch_processor.get_job_status(job_id)
        if status and status['status'] == 'completed':
            result = batch_processor.get_job_result(job_id)
            return result.results if result else []
        time.sleep(0.1)

def batch_generate_embeddings(texts: List[str]) -> List[List[float]]:
    """Generate embeddings for multiple texts in batch"""
    batch_processor = get_batch_processor()
    
    # Submit job
    job_id = batch_processor.submit_job(
        job_type='embedding',
        items=texts,
        priority=2
    )
    
    # Wait for completion
    while True:
        status = batch_processor.get_job_status(job_id)
        if status and status['status'] == 'completed':
            result = batch_processor.get_job_result(job_id)
            return [r for r in result.results if r is not None] if result else []
        time.sleep(0.1)

def batch_vector_search(queries: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
    """Perform vector search for multiple queries in batch"""
    batch_processor = get_batch_processor()
    
    # Submit job
    job_id = batch_processor.submit_job(
        job_type='vector_search',
        items=queries,
        priority=3
    )
    
    # Wait for completion
    while True:
        status = batch_processor.get_job_status(job_id)
        if status and status['status'] == 'completed':
            result = batch_processor.get_job_result(job_id)
            return [r for r in result.results if r is not None] if result else []
        time.sleep(0.1) 