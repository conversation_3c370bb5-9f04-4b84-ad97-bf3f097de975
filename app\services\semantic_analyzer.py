"""
Semantic Analyzer Service
Provides advanced query analysis, intent classification, and semantic complexity scoring
for improved retrieval performance.
"""

import logging
import re
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import numpy as np
from langchain_ollama.embeddings import OllamaEmbeddings
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)

class QueryIntent(Enum):
    """Query intent classification"""
    FACTUAL = "factual"           # What is X?
    ANALYTICAL = "analytical"      # How does X work?
    COMPARATIVE = "comparative"    # Compare X and Y
    PROCEDURAL = "procedural"      # How to do X?
    CAUSAL = "causal"             # Why does X happen?
    TEMPORAL = "temporal"         # When did X happen?
    SPATIAL = "spatial"           # Where is X located?
    QUANTITATIVE = "quantitative" # How much/many X?
    UNKNOWN = "unknown"

@dataclass
class QueryComplexity:
    """Query complexity analysis result"""
    semantic_score: float
    word_count: int
    unique_terms: int
    domain_terms: int
    intent: QueryIntent
    confidence: float
    suggested_k: int
    complexity_level: str  # 'simple', 'medium', 'complex'

@dataclass
class QueryAnalysis:
    """Complete query analysis result"""
    original_query: str
    complexity: QueryComplexity
    expanded_terms: List[str]
    domain_keywords: List[str]
    suggested_filters: Dict[str, Any]
    processing_metadata: Dict[str, Any]

class SemanticAnalyzer:
    """
    Advanced semantic analyzer for query processing and complexity assessment
    """
    
    def __init__(self, embedding_model: str = None):
        self.embedding_model = embedding_model or "mxbai-embed-large:latest"
        self._embedding_function = None
        
        # ERDB domain-specific keywords and terms
        self.domain_keywords = {
            'forestry': ['forest', 'trees', 'deforestation', 'reforestation', 'timber', 'logging', 'sustainable'],
            'environment': ['ecosystem', 'biodiversity', 'conservation', 'climate', 'pollution', 'sustainability'],
            'research': ['study', 'analysis', 'methodology', 'findings', 'conclusion', 'data', 'results'],
            'agriculture': ['farming', 'crops', 'soil', 'irrigation', 'pesticides', 'organic', 'yield'],
            'wildlife': ['animals', 'species', 'habitat', 'endangered', 'conservation', 'population'],
            'water': ['water', 'aquatic', 'marine', 'freshwater', 'watershed', 'pollution', 'quality'],
            'climate': ['climate', 'weather', 'temperature', 'precipitation', 'drought', 'flood', 'change']
        }
        
        # Intent classification patterns
        self.intent_patterns = {
            QueryIntent.FACTUAL: [
                r'\bwhat\s+is\b', r'\bwhat\s+are\b', r'\bdefine\b', r'\bdefinition\b',
                r'\bdescribe\b', r'\bexplain\b'
            ],
            QueryIntent.ANALYTICAL: [
                r'\bhow\s+does\b', r'\bhow\s+do\b', r'\bmechanism\b', r'\bprocess\b',
                r'\banalysis\b', r'\bstudy\b'
            ],
            QueryIntent.COMPARATIVE: [
                r'\bcompare\b', r'\bversus\b', r'\bvs\b', r'\bdifference\b',
                r'\bsimilar\b', r'\bcontrast\b'
            ],
            QueryIntent.PROCEDURAL: [
                r'\bhow\s+to\b', r'\bprocedure\b', r'\bmethod\b', r'\btechnique\b',
                r'\bstep\b', r'\bguide\b'
            ],
            QueryIntent.CAUSAL: [
                r'\bwhy\b', r'\bcause\b', r'\beffect\b', r'\bimpact\b',
                r'\binfluence\b', r'\bresult\b'
            ],
            QueryIntent.TEMPORAL: [
                r'\bwhen\b', r'\btime\b', r'\bperiod\b', r'\bhistory\b',
                r'\btrend\b', r'\bchange\b'
            ],
            QueryIntent.SPATIAL: [
                r'\bwhere\b', r'\blocation\b', r'\barea\b', r'\bregion\b',
                r'\bgeographic\b', r'\bdistribution\b'
            ],
            QueryIntent.QUANTITATIVE: [
                r'\bhow\s+much\b', r'\bhow\s+many\b', r'\bamount\b', r'\bquantity\b',
                r'\bpercentage\b', r'\bstatistics\b'
            ]
        }
        
        # Complexity indicators
        self.complexity_indicators = {
            'high_complexity': [
                'analysis', 'methodology', 'framework', 'comprehensive', 'detailed',
                'investigation', 'assessment', 'evaluation', 'comparison', 'integration'
            ],
            'medium_complexity': [
                'study', 'research', 'examine', 'explore', 'identify', 'determine',
                'assess', 'evaluate', 'analyze', 'investigate'
            ],
            'low_complexity': [
                'what', 'is', 'are', 'define', 'describe', 'list', 'name',
                'basic', 'simple', 'overview'
            ]
        }
    
    def _get_embedding_function(self):
        """Get or create the embedding function"""
        if self._embedding_function is None:
            try:
                self._embedding_function = OllamaEmbeddings(model=self.embedding_model)
                logger.info(f"Initialized embedding function with model: {self.embedding_model}")
            except Exception as e:
                logger.error(f"Failed to initialize embedding function: {str(e)}")
                raise
        return self._embedding_function
    
    @performance_monitor(track_memory=True, track_cpu=True)
    def analyze_query(self, query: str) -> QueryAnalysis:
        """
        Perform comprehensive query analysis
        
        Args:
            query: The user's query text
            
        Returns:
            QueryAnalysis object with complete analysis results
        """
        try:
            # Clean and normalize query
            clean_query = self._normalize_query(query)
            
            # Analyze complexity
            complexity = self._analyze_complexity(clean_query)
            
            # Classify intent
            intent = self._classify_intent(clean_query)
            complexity.intent = intent
            
            # Extract domain keywords
            domain_keywords = self._extract_domain_keywords(clean_query)
            
            # Generate expanded terms
            expanded_terms = self._expand_query_terms(clean_query, domain_keywords)
            
            # Generate suggested filters
            suggested_filters = self._generate_suggested_filters(clean_query, intent, domain_keywords)
            
            # Create processing metadata
            processing_metadata = {
                'original_length': len(query),
                'normalized_length': len(clean_query),
                'analysis_timestamp': self._get_timestamp(),
                'embedding_model': self.embedding_model
            }
            
            return QueryAnalysis(
                original_query=query,
                complexity=complexity,
                expanded_terms=expanded_terms,
                domain_keywords=domain_keywords,
                suggested_filters=suggested_filters,
                processing_metadata=processing_metadata
            )
            
        except Exception as e:
            logger.error(f"Error analyzing query '{query}': {str(e)}")
            # Return fallback analysis
            return self._create_fallback_analysis(query)
    
    def _normalize_query(self, query: str) -> str:
        """Normalize and clean query text"""
        # Convert to lowercase
        normalized = query.lower().strip()
        
        # Remove extra whitespace
        normalized = re.sub(r'\s+', ' ', normalized)
        
        # Remove punctuation except for important patterns
        normalized = re.sub(r'[^\w\s\-]', ' ', normalized)
        
        return normalized.strip()
    
    def _analyze_complexity(self, query: str) -> QueryComplexity:
        """Analyze query complexity using multiple factors"""
        words = query.split()
        word_count = len(words)
        unique_terms = len(set(words))
        
        # Calculate semantic complexity score
        semantic_score = self._calculate_semantic_complexity(query)
        
        # Count domain-specific terms
        domain_terms = self._count_domain_terms(query)
        
        # Determine complexity level
        complexity_level = self._determine_complexity_level(semantic_score, word_count, domain_terms)
        
        # Calculate suggested k value
        suggested_k = self._calculate_suggested_k(complexity_level, semantic_score, word_count)
        
        # Calculate confidence score
        confidence = self._calculate_confidence(semantic_score, word_count, domain_terms)
        
        return QueryComplexity(
            semantic_score=semantic_score,
            word_count=word_count,
            unique_terms=unique_terms,
            domain_terms=domain_terms,
            intent=QueryIntent.UNKNOWN,  # Will be set later
            confidence=confidence,
            suggested_k=suggested_k,
            complexity_level=complexity_level
        )
    
    def _calculate_semantic_complexity(self, query: str) -> float:
        """Calculate semantic complexity score based on query content"""
        score = 0.0
        words = query.split()
        
        # Base score from word count
        if len(words) <= 3:
            score += 0.2
        elif len(words) <= 8:
            score += 0.5
        else:
            score += 0.8
        
        # Add complexity based on domain terms
        domain_terms = self._count_domain_terms(query)
        score += min(0.3, domain_terms * 0.1)
        
        # Add complexity based on technical terms
        technical_terms = self._count_technical_terms(query)
        score += min(0.2, technical_terms * 0.05)
        
        # Add complexity based on complexity indicators
        complexity_boost = self._calculate_complexity_boost(query)
        score += complexity_boost
        
        return min(1.0, score)
    
    def _count_domain_terms(self, query: str) -> int:
        """Count domain-specific terms in the query"""
        count = 0
        query_lower = query.lower()
        
        for category, terms in self.domain_keywords.items():
            for term in terms:
                if term in query_lower:
                    count += 1
        
        return count
    
    def _count_technical_terms(self, query: str) -> int:
        """Count technical/scientific terms in the query"""
        technical_patterns = [
            r'\b\w+ology\b',  # Words ending in -ology
            r'\b\w+ation\b',  # Words ending in -ation
            r'\b\w+ment\b',   # Words ending in -ment
            r'\b\w+ity\b',    # Words ending in -ity
            r'\b\w+ism\b',    # Words ending in -ism
        ]
        
        count = 0
        for pattern in technical_patterns:
            matches = re.findall(pattern, query.lower())
            count += len(matches)
        
        return count
    
    def _calculate_complexity_boost(self, query: str) -> float:
        """Calculate complexity boost based on complexity indicators"""
        boost = 0.0
        query_lower = query.lower()
        
        # High complexity indicators
        for term in self.complexity_indicators['high_complexity']:
            if term in query_lower:
                boost += 0.15
        
        # Medium complexity indicators
        for term in self.complexity_indicators['medium_complexity']:
            if term in query_lower:
                boost += 0.08
        
        # Low complexity indicators (reduce score)
        for term in self.complexity_indicators['low_complexity']:
            if term in query_lower:
                boost -= 0.05
        
        return max(0.0, boost)
    
    def _determine_complexity_level(self, semantic_score: float, word_count: int, domain_terms: int) -> str:
        """Determine complexity level based on multiple factors"""
        # Calculate composite score
        composite_score = (semantic_score * 0.5) + (min(word_count / 10, 1.0) * 0.3) + (min(domain_terms / 5, 1.0) * 0.2)
        
        if composite_score < 0.3:
            return 'simple'
        elif composite_score < 0.7:
            return 'medium'
        else:
            return 'complex'
    
    def _calculate_suggested_k(self, complexity_level: str, semantic_score: float, word_count: int) -> int:
        """Calculate suggested k value based on complexity analysis"""
        base_k = 12
        
        if complexity_level == 'simple':
            k = max(6, int(base_k * 0.6))
        elif complexity_level == 'medium':
            k = base_k
        else:  # complex
            k = min(20, int(base_k * 1.4))
        
        # Adjust based on semantic score
        if semantic_score > 0.8:
            k = min(25, k + 3)
        elif semantic_score < 0.3:
            k = max(4, k - 2)
        
        return k
    
    def _calculate_confidence(self, semantic_score: float, word_count: int, domain_terms: int) -> float:
        """Calculate confidence in the analysis"""
        # Higher confidence with more words and domain terms
        word_confidence = min(1.0, word_count / 10)
        domain_confidence = min(1.0, domain_terms / 3)
        
        # Average of all confidence factors
        confidence = (semantic_score + word_confidence + domain_confidence) / 3
        return min(1.0, confidence)
    
    def _classify_intent(self, query: str) -> QueryIntent:
        """Classify query intent based on patterns"""
        query_lower = query.lower()
        
        # Check each intent pattern
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return intent
        
        # Default to unknown if no pattern matches
        return QueryIntent.UNKNOWN
    
    def _extract_domain_keywords(self, query: str) -> List[str]:
        """Extract domain-specific keywords from the query"""
        keywords = []
        query_lower = query.lower()
        
        for category, terms in self.domain_keywords.items():
            for term in terms:
                if term in query_lower:
                    keywords.append(term)
        
        return list(set(keywords))  # Remove duplicates
    
    def _expand_query_terms(self, query: str, domain_keywords: List[str]) -> List[str]:
        """Expand query with related terms"""
        expanded = query.split()
        
        # Add domain-related terms
        for keyword in domain_keywords:
            # Find related terms from the same domain
            for category, terms in self.domain_keywords.items():
                if keyword in terms:
                    # Add a few related terms
                    related_terms = [t for t in terms if t != keyword][:2]
                    expanded.extend(related_terms)
        
        # Add synonyms for common terms
        synonyms = {
            'forest': ['woodland', 'timberland', 'forestry'],
            'environment': ['ecosystem', 'habitat', 'natural'],
            'research': ['study', 'investigation', 'analysis'],
            'impact': ['effect', 'influence', 'consequence'],
            'conservation': ['preservation', 'protection', 'sustainability']
        }
        
        for term in query.split():
            if term in synonyms:
                expanded.extend(synonyms[term])
        
        return list(set(expanded))  # Remove duplicates
    
    def _generate_suggested_filters(self, query: str, intent: QueryIntent, domain_keywords: List[str]) -> Dict[str, Any]:
        """Generate suggested filters for the query"""
        filters = {}
        
        # Add domain-based filters
        if domain_keywords:
            filters['domain_keywords'] = domain_keywords
        
        # Add intent-based filters
        if intent != QueryIntent.UNKNOWN:
            filters['intent'] = intent.value
        
        # Add temporal filters for temporal queries
        if intent == QueryIntent.TEMPORAL:
            filters['temporal_focus'] = True
        
        # Add spatial filters for spatial queries
        if intent == QueryIntent.SPATIAL:
            filters['spatial_focus'] = True
        
        # Add quantitative filters for quantitative queries
        if intent == QueryIntent.QUANTITATIVE:
            filters['quantitative_focus'] = True
        
        return filters
    
    def _get_timestamp(self) -> str:
        """Get current timestamp string"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def _create_fallback_analysis(self, query: str) -> QueryAnalysis:
        """Create fallback analysis when main analysis fails"""
        return QueryAnalysis(
            original_query=query,
            complexity=QueryComplexity(
                semantic_score=0.5,
                word_count=len(query.split()),
                unique_terms=len(set(query.split())),
                domain_terms=0,
                intent=QueryIntent.UNKNOWN,
                confidence=0.3,
                suggested_k=12,
                complexity_level='medium'
            ),
            expanded_terms=query.split(),
            domain_keywords=[],
            suggested_filters={},
            processing_metadata={'fallback': True, 'error': 'Analysis failed'}
        )

# Global instance for easy access
_semantic_analyzer = None

def get_semantic_analyzer(embedding_model: str = None) -> SemanticAnalyzer:
    """Get or create global semantic analyzer instance"""
    global _semantic_analyzer
    if _semantic_analyzer is None:
        _semantic_analyzer = SemanticAnalyzer(embedding_model)
    return _semantic_analyzer

def analyze_query_semantics(query: str, embedding_model: str = None) -> QueryAnalysis:
    """Convenience function for query analysis"""
    analyzer = get_semantic_analyzer(embedding_model)
    return analyzer.analyze_query(query) 