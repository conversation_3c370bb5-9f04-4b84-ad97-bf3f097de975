"""
Intelligent Category Routing and Dynamic Category Weighting Service

This module provides advanced category routing capabilities for the ERDB AI system,
including intelligent category selection, dynamic weighting, and cross-category
optimization.
"""

import logging
import json
import re
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import numpy as np
from collections import defaultdict, Counter
import sqlite3
import os

from app.services.semantic_analyzer import analyze_query_semantics, QueryAnalysis, QueryComplexity, QueryIntent
from app.services.vector_db import get_vector_db
from app.utils.helpers import list_categories
from app.services.cache_service import cache_service

logger = logging.getLogger(__name__)

@dataclass
class CategoryWeight:
    """Represents the weight and confidence for a category"""
    category: str
    weight: float
    confidence: float
    reasoning: str
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class CategoryRoutingResult:
    """Result of category routing analysis"""
    primary_categories: List[CategoryWeight]
    secondary_categories: List[CategoryWeight]
    cross_category_threshold: float
    routing_strategy: str
    query_analysis: QueryAnalysis
    execution_time: float
    metadata: Dict[str, Any] = field(default_factory=dict)

class CategoryRouter:
    """
    Intelligent category routing system that determines the most relevant
    categories for a given query and assigns dynamic weights.
    """
    
    def __init__(self):
        self.categories = self._load_categories()
        self.category_keywords = self._build_category_keywords()
        self.category_stats = self._load_category_statistics()
        self.routing_cache = {}
        self.cache_ttl = 3600  # 1 hour cache
        
    def _load_categories(self) -> List[str]:
        """Load available categories from the system"""
        try:
            categories = list_categories()
            logger.info(f"Loaded {len(categories)} categories: {categories}")
            return categories
        except Exception as e:
            logger.error(f"Error loading categories: {e}")
            return []
    
    def _build_category_keywords(self) -> Dict[str, List[str]]:
        """Build keyword mappings for each category"""
        # Define category-specific keywords based on the ERDB domain
        keywords = {
            'CANOPY': [
                'canopy', 'forest', 'trees', 'forestry', 'timber', 'wood', 'logging',
                'silviculture', 'reforestation', 'afforestation', 'forest management',
                'sustainable forestry', 'forest conservation', 'tree species',
                'forest ecology', 'forest biodiversity', 'forest health'
            ],
            'RISE': [
                'rise', 'research', 'innovation', 'science', 'technology', 'development',
                'experiment', 'study', 'analysis', 'methodology', 'innovation',
                'scientific research', 'forest research', 'agricultural research',
                'environmental research', 'conservation research'
            ],
            'MANUAL': [
                'manual', 'guide', 'procedure', 'protocol', 'method', 'technique',
                'instruction', 'handbook', 'reference', 'best practice', 'standard',
                'operational manual', 'technical manual', 'field guide',
                'implementation guide', 'training manual'
            ]
        }
        
        # Add common domain keywords to all categories
        common_keywords = [
            'philippines', 'filipino', 'tropical', 'climate', 'environment',
            'conservation', 'biodiversity', 'ecosystem', 'natural resources',
            'sustainability', 'development', 'agriculture', 'rural', 'community'
        ]
        
        for category in keywords:
            keywords[category].extend(common_keywords)
            
        return keywords
    
    def _load_category_statistics(self) -> Dict[str, Dict[str, Any]]:
        """Load category statistics for weighting decisions"""
        stats = {}
        
        try:
            # Get vector database for statistics - use first available category
            if self.categories:
                vector_db = get_vector_db(self.categories[0])
                if vector_db and hasattr(vector_db, 'collection'):
                    collection = vector_db.collection
                
                for category in self.categories:
                    try:
                        # Get document count for category
                        result = collection.get(
                            where={"category": category},
                            include=['metadatas']
                        )
                        
                        doc_count = len(result['ids']) if result['ids'] else 0
                        
                        # Calculate average document length
                        avg_length = 0
                        if doc_count > 0:
                            total_length = sum(
                                len(metadata.get('content', '')) 
                                for metadata in result['metadatas']
                            )
                            avg_length = total_length / doc_count
                        
                        stats[category] = {
                            'document_count': doc_count,
                            'average_length': avg_length,
                            'last_updated': datetime.now().isoformat()
                        }
                        
                    except Exception as e:
                        logger.warning(f"Error loading stats for category {category}: {e}")
                        stats[category] = {
                            'document_count': 0,
                            'average_length': 0,
                            'last_updated': datetime.now().isoformat()
                        }
                        
        except Exception as e:
            logger.error(f"Error loading category statistics: {e}")
            
        return stats
    
    def route_query(self, query: str, user_context: Optional[Dict[str, Any]] = None) -> CategoryRoutingResult:
        """
        Route a query to the most relevant categories with dynamic weighting.
        
        Args:
            query: The user query
            user_context: Optional user context for personalized routing
            
        Returns:
            CategoryRoutingResult with category weights and routing strategy
        """
        start_time = datetime.now()
        
        # Check cache first
        cache_key = f"category_route:{hash(query)}"
        cached_result = cache_service.get(cache_key)
        if cached_result:
            logger.info("Using cached category routing result")
            return CategoryRoutingResult(**cached_result)
        
        try:
            # Analyze query semantics
            query_analysis = analyze_query_semantics(query)
            
            # Determine routing strategy
            routing_strategy = self._determine_routing_strategy(query_analysis)
            
            # Calculate category weights
            category_weights = self._calculate_category_weights(query, query_analysis, user_context)
            
            # Separate primary and secondary categories
            primary_categories = []
            secondary_categories = []
            
            for weight in category_weights:
                if weight.weight >= 0.5:  # High confidence threshold
                    primary_categories.append(weight)
                elif weight.weight >= 0.2:  # Medium confidence threshold
                    secondary_categories.append(weight)
            
            # Sort by weight
            primary_categories.sort(key=lambda x: x.weight, reverse=True)
            secondary_categories.sort(key=lambda x: x.weight, reverse=True)
            
            # Determine cross-category threshold
            cross_category_threshold = self._calculate_cross_category_threshold(
                query_analysis, primary_categories
            )
            
            # Create result
            result = CategoryRoutingResult(
                primary_categories=primary_categories,
                secondary_categories=secondary_categories,
                cross_category_threshold=cross_category_threshold,
                routing_strategy=routing_strategy,
                query_analysis=query_analysis,
                execution_time=(datetime.now() - start_time).total_seconds(),
                metadata={
                    'total_categories_analyzed': len(category_weights),
                    'cache_hit': False
                }
            )
            
            # Cache the result
            cache_service.set(cache_key, result.__dict__, ttl=self.cache_ttl)
            
            logger.info(f"Category routing completed in {result.execution_time:.3f}s")
            logger.info(f"Primary categories: {[c.category for c in primary_categories]}")
            logger.info(f"Routing strategy: {routing_strategy}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in category routing: {e}")
            # Return fallback result
            return self._create_fallback_result(query, start_time)
    
    def _determine_routing_strategy(self, query_analysis: QueryAnalysis) -> str:
        """Determine the best routing strategy based on query analysis"""
        
        # Check for specific category indicators
        if query_analysis.domain_keywords:
            domain_keywords = set(query_analysis.domain_keywords)
            
            if any(keyword in domain_keywords for keyword in ['research', 'study', 'experiment']):
                return 'research_focused'
            elif any(keyword in domain_keywords for keyword in ['manual', 'guide', 'procedure']):
                return 'manual_focused'
            elif any(keyword in domain_keywords for keyword in ['forest', 'canopy', 'trees']):
                return 'forestry_focused'
        
        # Check complexity for strategy selection
        if query_analysis.complexity.complexity_level == 'high':
            return 'comprehensive_search'
        elif query_analysis.complexity.complexity_level == 'medium':
            return 'balanced_search'
        else:
            return 'focused_search'
    
    def _calculate_category_weights(self, query: str, query_analysis: QueryAnalysis, 
                                  user_context: Optional[Dict[str, Any]] = None) -> List[CategoryWeight]:
        """Calculate weights for each category based on multiple factors"""
        
        weights = []
        query_lower = query.lower()
        
        for category in self.categories:
            weight = 0.0
            confidence = 0.0
            reasoning = []
            
            # 1. Keyword matching (40% weight)
            keyword_score = self._calculate_keyword_score(query_lower, category)
            weight += keyword_score * 0.4
            if keyword_score > 0:
                reasoning.append(f"Keyword match: {keyword_score:.2f}")
            
            # 2. Semantic similarity (30% weight)
            semantic_score = self._calculate_semantic_score(query_analysis, category)
            weight += semantic_score * 0.3
            if semantic_score > 0:
                reasoning.append(f"Semantic similarity: {semantic_score:.2f}")
            
            # 3. Category statistics (15% weight)
            stats_score = self._calculate_statistics_score(category)
            weight += stats_score * 0.15
            if stats_score > 0:
                reasoning.append(f"Category statistics: {stats_score:.2f}")
            
            # 4. User context (10% weight)
            context_score = self._calculate_context_score(category, user_context)
            weight += context_score * 0.1
            if context_score > 0:
                reasoning.append(f"User context: {context_score:.2f}")
            
            # 5. Query intent alignment (5% weight)
            intent_score = self._calculate_intent_score(query_analysis, category)
            weight += intent_score * 0.05
            if intent_score > 0:
                reasoning.append(f"Intent alignment: {intent_score:.2f}")
            
            # Calculate confidence based on reasoning strength
            confidence = min(1.0, len(reasoning) * 0.2 + weight * 0.8)
            
            # Create category weight object
            category_weight = CategoryWeight(
                category=category,
                weight=weight,
                confidence=confidence,
                reasoning='; '.join(reasoning) if reasoning else "No specific indicators",
                metadata={
                    'keyword_score': keyword_score,
                    'semantic_score': semantic_score,
                    'stats_score': stats_score,
                    'context_score': context_score,
                    'intent_score': intent_score
                }
            )
            
            weights.append(category_weight)
        
        return weights
    
    def _calculate_keyword_score(self, query: str, category: str) -> float:
        """Calculate keyword matching score for a category"""
        if category not in self.category_keywords:
            return 0.0
        
        category_keywords = self.category_keywords[category]
        query_words = set(query.split())
        
        # Count exact matches
        exact_matches = sum(1 for keyword in category_keywords if keyword in query_words)
        
        # Count partial matches (substring)
        partial_matches = sum(1 for keyword in category_keywords 
                            if any(keyword in word or word in keyword for word in query_words))
        
        # Calculate score
        total_keywords = len(category_keywords)
        if total_keywords == 0:
            return 0.0
        
        score = (exact_matches * 0.7 + partial_matches * 0.3) / total_keywords
        return min(1.0, score * 2)  # Normalize to 0-1 range
    
    def _calculate_semantic_score(self, query_analysis: QueryAnalysis, category: str) -> float:
        """Calculate semantic similarity score for a category"""
        if not query_analysis.domain_keywords:
            return 0.0
        
        # Map domain keywords to category relevance
        category_domain_mapping = {
            'CANOPY': ['forest', 'trees', 'forestry', 'timber', 'wood', 'logging'],
            'RISE': ['research', 'study', 'experiment', 'analysis', 'innovation'],
            'MANUAL': ['manual', 'guide', 'procedure', 'method', 'technique']
        }
        
        if category not in category_domain_mapping:
            return 0.0
        
        category_domains = set(category_domain_mapping[category])
        query_domains = set(query_analysis.domain_keywords)
        
        # Calculate Jaccard similarity
        intersection = len(category_domains.intersection(query_domains))
        union = len(category_domains.union(query_domains))
        
        if union == 0:
            return 0.0
        
        return intersection / union
    
    def _calculate_statistics_score(self, category: str) -> float:
        """Calculate score based on category statistics"""
        if category not in self.category_stats:
            return 0.0
        
        stats = self.category_stats[category]
        doc_count = stats.get('document_count', 0)
        avg_length = stats.get('average_length', 0)
        
        # Normalize document count (0-1000 docs = 0-1 score)
        doc_score = min(1.0, doc_count / 1000.0)
        
        # Normalize average length (0-5000 chars = 0-1 score)
        length_score = min(1.0, avg_length / 5000.0)
        
        # Combine scores (favor document count more)
        return doc_score * 0.7 + length_score * 0.3
    
    def _calculate_context_score(self, category: str, user_context: Optional[Dict[str, Any]]) -> float:
        """Calculate score based on user context"""
        if not user_context:
            return 0.0
        
        # Check for recent category usage
        recent_categories = user_context.get('recent_categories', [])
        if category in recent_categories:
            # Boost score for recently used categories
            return 0.3
        
        # Check for user preferences
        preferred_categories = user_context.get('preferred_categories', [])
        if category in preferred_categories:
            return 0.5
        
        return 0.0
    
    def _calculate_intent_score(self, query_analysis: QueryAnalysis, category: str) -> float:
        """Calculate score based on query intent alignment"""
        intent_mapping = {
            'factual': {'CANOPY': 0.8, 'RISE': 0.6, 'MANUAL': 0.4},
            'analytical': {'CANOPY': 0.6, 'RISE': 0.9, 'MANUAL': 0.3},
            'procedural': {'CANOPY': 0.4, 'RISE': 0.3, 'MANUAL': 0.9}
        }
        
        intent = query_analysis.complexity.intent.value
        if intent in intent_mapping and category in intent_mapping[intent]:
            return intent_mapping[intent][category]
        
        return 0.0
    
    def _calculate_cross_category_threshold(self, query_analysis: QueryAnalysis, 
                                          primary_categories: List[CategoryWeight]) -> float:
        """Calculate threshold for cross-category search"""
        
        # Base threshold on query complexity
        base_threshold = {
            'low': 0.3,
            'medium': 0.5,
            'high': 0.7
        }.get(query_analysis.complexity.complexity_level, 0.5)
        
        # Adjust based on number of primary categories
        if len(primary_categories) > 1:
            # Multiple strong categories - lower threshold for cross-category
            base_threshold *= 0.8
        elif len(primary_categories) == 0:
            # No strong categories - higher threshold to avoid noise
            base_threshold *= 1.2
        
        return min(1.0, max(0.1, base_threshold))
    
    def _create_fallback_result(self, query: str, start_time: datetime) -> CategoryRoutingResult:
        """Create a fallback result when routing fails"""
        return CategoryRoutingResult(
            primary_categories=[
                CategoryWeight(
                    category='CANOPY',
                    weight=0.5,
                    confidence=0.3,
                    reasoning="Fallback routing due to error"
                )
            ],
            secondary_categories=[],
            cross_category_threshold=0.5,
            routing_strategy='fallback',
            query_analysis=QueryAnalysis(
                original_query=query,
                complexity=QueryComplexity(
                    semantic_score=0.5,
                    word_count=len(query.split()),
                    unique_terms=len(set(query.split())),
                    domain_terms=0,
                    intent=QueryIntent.FACTUAL,
                    confidence=0.5,
                    suggested_k=10,
                    complexity_level='medium'
                ),
                expanded_terms=[],
                domain_keywords=[],
                suggested_filters={},
                processing_metadata={}
            ),
            execution_time=(datetime.now() - start_time).total_seconds(),
            metadata={'error': True}
        )
    
    def get_category_statistics(self) -> Dict[str, Dict[str, Any]]:
        """Get current category statistics"""
        return self.category_stats.copy()
    
    def update_category_statistics(self):
        """Update category statistics from the database"""
        self.category_stats = self._load_category_statistics()
        logger.info("Category statistics updated")

# Global instance
category_router = CategoryRouter()

def route_query_to_categories(query: str, user_context: Optional[Dict[str, Any]] = None) -> CategoryRoutingResult:
    """
    Route a query to the most relevant categories.
    
    Args:
        query: The user query
        user_context: Optional user context for personalized routing
        
    Returns:
        CategoryRoutingResult with category weights and routing strategy
    """
    return category_router.route_query(query, user_context)

def get_category_weights(query: str, user_context: Optional[Dict[str, Any]] = None) -> List[CategoryWeight]:
    """
    Get category weights for a query.
    
    Args:
        query: The user query
        user_context: Optional user context
        
    Returns:
        List of CategoryWeight objects sorted by weight
    """
    result = route_query_to_categories(query, user_context)
    all_categories = result.primary_categories + result.secondary_categories
    return sorted(all_categories, key=lambda x: x.weight, reverse=True) 