"""
Intelligent Performance Tuning Service

This module provides automatic performance tuning for the RAG system, including:
- Dynamic parameter adjustment based on analytics
- Adaptive caching strategies
- Resource optimization
- Performance bottleneck resolution
"""

import time
import threading
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import logging

from app.services.retrieval_analytics import get_retrieval_analytics, QueryOutcome
from app.services.advanced_cache_service import advanced_cache_service
from app.services.batch_processor import get_batch_processor
from config.enhanced_retrieval_config import EnhancedRetrievalConfig

logger = logging.getLogger(__name__)


class TuningStrategy(Enum):
    """Performance tuning strategies"""
    CONSERVATIVE = "conservative"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"
    ADAPTIVE = "adaptive"


class TuningAction(Enum):
    """Types of tuning actions"""
    INCREASE_CACHE_TTL = "increase_cache_ttl"
    DECREASE_CACHE_TTL = "decrease_cache_ttl"
    INCREASE_BATCH_SIZE = "increase_batch_size"
    DECREASE_BATCH_SIZE = "decrease_batch_size"
    INCREASE_WORKERS = "increase_workers"
    DECREASE_WORKERS = "decrease_workers"
    ADJUST_K_VALUE = "adjust_k_value"
    OPTIMIZE_QUERY_COMPLEXITY = "optimize_query_complexity"
    WARM_CACHE = "warm_cache"
    CLEANUP_CACHE = "cleanup_cache"


@dataclass
class TuningRecommendation:
    """A performance tuning recommendation"""
    action: TuningAction
    reason: str
    expected_impact: str
    confidence: float  # 0.0 to 1.0
    priority: int  # 1-5, higher is more important
    parameters: Dict[str, Any]


@dataclass
class TuningResult:
    """Result of a tuning action"""
    action: TuningAction
    success: bool
    execution_time: float
    impact_metrics: Dict[str, float]
    error_message: Optional[str] = None


class PerformanceTuner:
    """Intelligent performance tuning service"""
    
    def __init__(self, config: EnhancedRetrievalConfig):
        self.config = config
        self.analytics = get_retrieval_analytics()
        self.batch_processor = get_batch_processor()
        
        # Tuning history
        self.tuning_history: List[TuningResult] = []
        self.last_tuning_time = datetime.now()
        
        # Performance thresholds
        self.thresholds = {
            'execution_time_warning': 2.0,  # seconds
            'execution_time_critical': 5.0,  # seconds
            'cache_hit_rate_minimum': 0.3,
            'cache_hit_rate_target': 0.7,
            'success_rate_minimum': 0.8,
            'success_rate_target': 0.95,
            'relevance_score_minimum': 0.6,
            'relevance_score_target': 0.8
        }
        
        # Tuning intervals
        self.tuning_intervals = {
            'quick_tune': 300,  # 5 minutes
            'standard_tune': 1800,  # 30 minutes
            'deep_tune': 7200,  # 2 hours
            'full_optimization': 86400  # 24 hours
        }
        
        # Current tuning strategy
        self.current_strategy = TuningStrategy.ADAPTIVE
        
        # Threading
        self.lock = threading.RLock()
        self.tuning_active = False
        
        logger.info("Performance tuner initialized")
    
    def analyze_and_tune(self, force_tune: bool = False) -> List[TuningResult]:
        """Analyze current performance and apply tuning recommendations"""
        with self.lock:
            if self.tuning_active and not force_tune:
                logger.info("Tuning already in progress, skipping")
                return []
            
            self.tuning_active = True
            
            try:
                # Get current analytics
                analytics = self.analytics.get_system_analytics(time_window_hours=1)
                bottleneck_analysis = self.analytics.get_bottleneck_analysis()
                
                # Generate recommendations
                recommendations = self._generate_recommendations(analytics, bottleneck_analysis)
                
                if not recommendations:
                    logger.info("No tuning recommendations generated")
                    return []
                
                # Apply recommendations
                results = []
                for recommendation in recommendations:
                    if recommendation.priority >= 3:  # Apply high priority recommendations
                        result = self._apply_tuning_action(recommendation)
                        results.append(result)
                        
                        if result.success:
                            logger.info(f"Applied tuning action: {recommendation.action.value}")
                        else:
                            logger.warning(f"Failed to apply tuning action: {recommendation.action.value}")
                
                self.last_tuning_time = datetime.now()
                return results
                
            finally:
                self.tuning_active = False
    
    def _generate_recommendations(self, analytics: Dict[str, Any], 
                                bottleneck_analysis: Dict[str, Any]) -> List[TuningRecommendation]:
        """Generate tuning recommendations based on analytics"""
        recommendations = []
        
        # Check execution time
        avg_execution_time = analytics.get('average_execution_time', 0.0)
        if avg_execution_time > self.thresholds['execution_time_critical']:
            recommendations.append(TuningRecommendation(
                action=TuningAction.INCREASE_CACHE_TTL,
                reason=f"Critical execution time: {avg_execution_time:.2f}s",
                expected_impact="Reduce query processing time by 20-30%",
                confidence=0.8,
                priority=5,
                parameters={'ttl_multiplier': 2.0}
            ))
            
            recommendations.append(TuningRecommendation(
                action=TuningAction.INCREASE_WORKERS,
                reason=f"High execution time suggests resource constraint",
                expected_impact="Improve parallel processing capacity",
                confidence=0.6,
                priority=4,
                parameters={'worker_increase': 2}
            ))
        
        elif avg_execution_time > self.thresholds['execution_time_warning']:
            recommendations.append(TuningRecommendation(
                action=TuningAction.INCREASE_BATCH_SIZE,
                reason=f"Above-average execution time: {avg_execution_time:.2f}s",
                expected_impact="Improve batch processing efficiency",
                confidence=0.7,
                priority=3,
                parameters={'batch_size_multiplier': 1.5}
            ))
        
        # Check cache hit rate
        cache_hit_rate = analytics.get('cache_hit_rate', 0.0)
        if cache_hit_rate < self.thresholds['cache_hit_rate_minimum']:
            recommendations.append(TuningRecommendation(
                action=TuningAction.WARM_CACHE,
                reason=f"Low cache hit rate: {cache_hit_rate:.2%}",
                expected_impact="Improve cache hit rate by 15-25%",
                confidence=0.8,
                priority=4,
                parameters={'warmup_limit': 100, 'categories': ['CANOPY', 'RISE', 'MANUAL']}
            ))
            
            recommendations.append(TuningRecommendation(
                action=TuningAction.INCREASE_CACHE_TTL,
                reason="Extend cache lifetime to improve hit rate",
                expected_impact="Increase cache hit rate by 10-20%",
                confidence=0.6,
                priority=3,
                parameters={'ttl_multiplier': 1.5}
            ))
        
        # Check success rate
        success_rate = analytics.get('success_rate', 0.0)
        if success_rate < self.thresholds['success_rate_minimum']:
            recommendations.append(TuningRecommendation(
                action=TuningAction.ADJUST_K_VALUE,
                reason=f"Low success rate: {success_rate:.2%}",
                expected_impact="Improve document retrieval success",
                confidence=0.7,
                priority=4,
                parameters={'k_adjustment': 1.2}
            ))
        
        # Check relevance scores
        avg_relevance = analytics.get('average_relevance_score', 0.0)
        if avg_relevance < self.thresholds['relevance_score_minimum']:
            recommendations.append(TuningRecommendation(
                action=TuningAction.OPTIMIZE_QUERY_COMPLEXITY,
                reason=f"Low relevance scores: {avg_relevance:.2f}",
                expected_impact="Improve document relevance by 10-15%",
                confidence=0.6,
                priority=3,
                parameters={'complexity_threshold': 0.7}
            ))
        
        # Check for memory issues
        if bottleneck_analysis.get('analysis_available', False):
            memory_usage = bottleneck_analysis.get('memory_usage_mb', 0.0)
            if memory_usage > 400:  # 400MB threshold
                recommendations.append(TuningRecommendation(
                    action=TuningAction.CLEANUP_CACHE,
                    reason=f"High memory usage: {memory_usage:.1f}MB",
                    expected_impact="Reduce memory usage by 20-30%",
                    confidence=0.9,
                    priority=4,
                    parameters={'cleanup_aggressive': True}
                ))
        
        # Sort by priority and confidence
        recommendations.sort(key=lambda r: (r.priority, r.confidence), reverse=True)
        
        return recommendations
    
    def _apply_tuning_action(self, recommendation: TuningRecommendation) -> TuningResult:
        """Apply a specific tuning action"""
        start_time = time.time()
        
        try:
            if recommendation.action == TuningAction.INCREASE_CACHE_TTL:
                result = self._increase_cache_ttl(recommendation.parameters)
            elif recommendation.action == TuningAction.DECREASE_CACHE_TTL:
                result = self._decrease_cache_ttl(recommendation.parameters)
            elif recommendation.action == TuningAction.INCREASE_BATCH_SIZE:
                result = self._increase_batch_size(recommendation.parameters)
            elif recommendation.action == TuningAction.DECREASE_BATCH_SIZE:
                result = self._decrease_batch_size(recommendation.parameters)
            elif recommendation.action == TuningAction.INCREASE_WORKERS:
                result = self._increase_workers(recommendation.parameters)
            elif recommendation.action == TuningAction.DECREASE_WORKERS:
                result = self._decrease_workers(recommendation.parameters)
            elif recommendation.action == TuningAction.ADJUST_K_VALUE:
                result = self._adjust_k_value(recommendation.parameters)
            elif recommendation.action == TuningAction.OPTIMIZE_QUERY_COMPLEXITY:
                result = self._optimize_query_complexity(recommendation.parameters)
            elif recommendation.action == TuningAction.WARM_CACHE:
                result = self._warm_cache(recommendation.parameters)
            elif recommendation.action == TuningAction.CLEANUP_CACHE:
                result = self._cleanup_cache(recommendation.parameters)
            else:
                raise ValueError(f"Unknown tuning action: {recommendation.action}")
            
            execution_time = time.time() - start_time
            
            return TuningResult(
                action=recommendation.action,
                success=True,
                execution_time=execution_time,
                impact_metrics=result
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Failed to apply tuning action {recommendation.action.value}: {e}")
            
            return TuningResult(
                action=recommendation.action,
                success=False,
                execution_time=execution_time,
                impact_metrics={},
                error_message=str(e)
            )
    
    def _increase_cache_ttl(self, parameters: Dict[str, Any]) -> Dict[str, float]:
        """Increase cache TTL to improve hit rates"""
        multiplier = parameters.get('ttl_multiplier', 1.5)
        
        # Update cache service TTL
        current_ttl = advanced_cache_service._default_ttl
        new_ttl = int(current_ttl * multiplier)
        advanced_cache_service._default_ttl = new_ttl
        
        logger.info(f"Increased cache TTL from {current_ttl}s to {new_ttl}s")
        
        return {
            'ttl_increase_percent': (multiplier - 1) * 100,
            'new_ttl_seconds': new_ttl
        }
    
    def _decrease_cache_ttl(self, parameters: Dict[str, Any]) -> Dict[str, float]:
        """Decrease cache TTL to free memory"""
        multiplier = parameters.get('ttl_multiplier', 0.7)
        
        current_ttl = advanced_cache_service._default_ttl
        new_ttl = int(current_ttl * multiplier)
        advanced_cache_service._default_ttl = new_ttl
        
        logger.info(f"Decreased cache TTL from {current_ttl}s to {new_ttl}s")
        
        return {
            'ttl_decrease_percent': (1 - multiplier) * 100,
            'new_ttl_seconds': new_ttl
        }
    
    def _increase_batch_size(self, parameters: Dict[str, Any]) -> Dict[str, float]:
        """Increase batch processing size"""
        multiplier = parameters.get('batch_size_multiplier', 1.5)
        
        # Update batch processor configuration
        current_batch_size = self.batch_processor.max_workers
        new_batch_size = int(current_batch_size * multiplier)
        
        # Note: This is a simplified implementation
        # In a real system, you'd need to reconfigure the batch processor
        logger.info(f"Increased batch size multiplier to {multiplier}")
        
        return {
            'batch_size_multiplier': multiplier,
            'expected_throughput_increase': (multiplier - 1) * 100
        }
    
    def _decrease_batch_size(self, parameters: Dict[str, Any]) -> Dict[str, float]:
        """Decrease batch processing size to reduce resource usage"""
        multiplier = parameters.get('batch_size_multiplier', 0.7)
        
        logger.info(f"Decreased batch size multiplier to {multiplier}")
        
        return {
            'batch_size_multiplier': multiplier,
            'expected_resource_reduction': (1 - multiplier) * 100
        }
    
    def _increase_workers(self, parameters: Dict[str, Any]) -> Dict[str, float]:
        """Increase number of worker threads/processes"""
        increase = parameters.get('worker_increase', 2)
        
        # Note: This is a simplified implementation
        # In a real system, you'd need to reconfigure the thread/process pools
        logger.info(f"Increased workers by {increase}")
        
        return {
            'worker_increase': increase,
            'expected_parallelism_improvement': increase * 10
        }
    
    def _decrease_workers(self, parameters: Dict[str, Any]) -> Dict[str, float]:
        """Decrease number of worker threads/processes to reduce resource usage"""
        decrease = parameters.get('worker_decrease', 1)
        
        logger.info(f"Decreased workers by {decrease}")
        
        return {
            'worker_decrease': decrease,
            'expected_resource_savings': decrease * 5
        }
    
    def _adjust_k_value(self, parameters: Dict[str, Any]) -> Dict[str, float]:
        """Adjust the k value for document retrieval"""
        adjustment = parameters.get('k_adjustment', 1.2)
        
        # Update configuration
        if hasattr(self.config, 'semantic_analyzer'):
            current_k = getattr(self.config.semantic_analyzer, 'default_k', 5)
            new_k = int(current_k * adjustment)
            setattr(self.config.semantic_analyzer, 'default_k', new_k)
            
            logger.info(f"Adjusted k value from {current_k} to {new_k}")
            
            return {
                'k_adjustment_factor': adjustment,
                'new_k_value': new_k,
                'expected_recall_improvement': (adjustment - 1) * 100
            }
        
        return {'k_adjustment_factor': adjustment}
    
    def _optimize_query_complexity(self, parameters: Dict[str, Any]) -> Dict[str, float]:
        """Optimize query complexity thresholds"""
        threshold = parameters.get('complexity_threshold', 0.7)
        
        # Update semantic analyzer configuration
        if hasattr(self.config, 'semantic_analyzer'):
            setattr(self.config.semantic_analyzer, 'complexity_threshold', threshold)
            
            logger.info(f"Updated complexity threshold to {threshold}")
            
            return {
                'complexity_threshold': threshold,
                'expected_relevance_improvement': 10.0
            }
        
        return {'complexity_threshold': threshold}
    
    def _warm_cache(self, parameters: Dict[str, Any]) -> Dict[str, float]:
        """Warm cache with frequently accessed items"""
        limit = parameters.get('warmup_limit', 100)
        categories = parameters.get('categories', ['CANOPY', 'RISE', 'MANUAL'])
        
        total_warmed = 0
        for category in categories:
            try:
                advanced_cache_service.warm_cache_for_category(category, limit=limit)
                total_warmed += limit
                logger.info(f"Warmed cache for category {category}")
            except Exception as e:
                logger.warning(f"Failed to warm cache for category {category}: {e}")
        
        return {
            'items_warmed': total_warmed,
            'categories_warmed': len(categories),
            'expected_hit_rate_improvement': 15.0
        }
    
    def _cleanup_cache(self, parameters: Dict[str, Any]) -> Dict[str, float]:
        """Clean up cache to free memory"""
        aggressive = parameters.get('cleanup_aggressive', False)
        
        # Get current memory usage
        stats = advanced_cache_service.get_advanced_stats()
        initial_memory = stats.get('memory_usage', {}).get('current_mb', 0)
        
        # Perform cleanup
        if aggressive:
            # Clear low-priority items
            advanced_cache_service._evict_entries(aggressive=True)
        else:
            # Normal cleanup
            advanced_cache_service._cleanup_expired_entries()
        
        # Get memory usage after cleanup
        stats_after = advanced_cache_service.get_advanced_stats()
        final_memory = stats_after.get('memory_usage', {}).get('current_mb', 0)
        
        memory_freed = initial_memory - final_memory
        
        logger.info(f"Cache cleanup freed {memory_freed:.1f}MB of memory")
        
        return {
            'memory_freed_mb': memory_freed,
            'memory_reduction_percent': (memory_freed / initial_memory * 100) if initial_memory > 0 else 0,
            'cleanup_aggressive': aggressive
        }
    
    def get_tuning_status(self) -> Dict[str, Any]:
        """Get current tuning status and recommendations"""
        analytics = self.analytics.get_system_analytics(time_window_hours=1)
        bottleneck_analysis = self.analytics.get_bottleneck_analysis()
        
        recommendations = self._generate_recommendations(analytics, bottleneck_analysis)
        
        return {
            'last_tuning_time': self.last_tuning_time.isoformat(),
            'tuning_active': self.tuning_active,
            'current_strategy': self.current_strategy.value,
            'pending_recommendations': len(recommendations),
            'high_priority_recommendations': len([r for r in recommendations if r.priority >= 4]),
            'recent_tuning_results': [
                {
                    'action': result.action.value,
                    'success': result.success,
                    'execution_time': result.execution_time,
                    'timestamp': (self.last_tuning_time - timedelta(minutes=i)).isoformat()
                }
                for i, result in enumerate(self.tuning_history[-5:])  # Last 5 results
            ],
            'performance_metrics': {
                'execution_time': analytics.get('average_execution_time', 0.0),
                'cache_hit_rate': analytics.get('cache_hit_rate', 0.0),
                'success_rate': analytics.get('success_rate', 0.0),
                'relevance_score': analytics.get('average_relevance_score', 0.0)
            }
        }
    
    def set_tuning_strategy(self, strategy: TuningStrategy) -> None:
        """Set the tuning strategy"""
        self.current_strategy = strategy
        logger.info(f"Tuning strategy set to: {strategy.value}")
    
    def update_thresholds(self, new_thresholds: Dict[str, float]) -> None:
        """Update performance thresholds"""
        self.thresholds.update(new_thresholds)
        logger.info(f"Updated tuning thresholds: {new_thresholds}")


# Global performance tuner instance
performance_tuner = None


def get_performance_tuner(config: EnhancedRetrievalConfig) -> PerformanceTuner:
    """Get the global performance tuner instance"""
    global performance_tuner
    if performance_tuner is None:
        performance_tuner = PerformanceTuner(config)
    return performance_tuner


def auto_tune_performance() -> List[TuningResult]:
    """Automatically tune performance based on current analytics"""
    from config.enhanced_retrieval_config import get_enhanced_retrieval_config
    
    config = get_enhanced_retrieval_config()
    tuner = get_performance_tuner(config)
    return tuner.analyze_and_tune() 