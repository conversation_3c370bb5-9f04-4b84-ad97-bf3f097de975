"""
Advanced Query Understanding and Expansion Service

This module provides sophisticated query analysis, expansion, and understanding
capabilities to improve retrieval accuracy and relevance.
"""

import re
import logging
from typing import List, Dict, Set, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from collections import defaultdict

from app.services.semantic_analyzer import Seman<PERSON><PERSON><PERSON><PERSON><PERSON>, QueryAnalysis
from app.utils.rag_performance import track_query_expansion_metrics
from config.enhanced_retrieval_config import EnhancedRetrievalConfig

logger = logging.getLogger(__name__)


@dataclass
class QueryExpansion:
    """Represents an expanded query with multiple variations"""
    original_query: str
    expanded_queries: List[str]
    expansion_type: str
    confidence: float
    reasoning: str
    timestamp: datetime


@dataclass
class QueryUnderstanding:
    """Represents deep understanding of a query"""
    query: str
    entities: List[str]
    relationships: List[Tuple[str, str, str]]  # (entity1, relation, entity2)
    context_clues: List[str]
    implicit_requirements: List[str]
    domain_specific_terms: List[str]
    temporal_context: Optional[str]
    spatial_context: Optional[str]
    confidence: float


@dataclass
class QueryVariation:
    """Represents a query variation for different search strategies"""
    query: str
    strategy: str
    weight: float
    reasoning: str
    expected_documents: int


class QueryExpander:
    """
    Advanced query understanding and expansion service
    
    Provides:
    - Query understanding and entity extraction
    - Semantic expansion with synonyms and related terms
    - Context-aware query variations
    - Domain-specific expansion
    - Query reformulation for different search strategies
    """
    
    def __init__(self, config: EnhancedRetrievalConfig = None):
        self.config = config or EnhancedRetrievalConfig()
        self.semantic_analyzer = SemanticAnalyzer(self.config.semantic_analyzer)
        
        # Domain-specific knowledge bases
        self.domain_synonyms = self._load_domain_synonyms()
        self.entity_patterns = self._load_entity_patterns()
        self.context_keywords = self._load_context_keywords()
        
        # Expansion strategies
        self.expansion_strategies = {
            'synonym': self._expand_synonyms,
            'related': self._expand_related_terms,
            'contextual': self._expand_contextual,
            'domain': self._expand_domain_specific,
            'reformulation': self._reformulate_query
        }
    
    def _load_domain_synonyms(self) -> Dict[str, List[str]]:
        """Load domain-specific synonyms and related terms"""
        return {
            'forestry': {
                'tree': ['timber', 'wood', 'forest', 'plantation'],
                'forest': ['woodland', 'timberland', 'forestry', 'grove'],
                'harvest': ['logging', 'cutting', 'extraction', 'removal'],
                'sustainability': ['conservation', 'preservation', 'management'],
                'biodiversity': ['wildlife', 'ecosystem', 'species', 'habitat']
            },
            'agriculture': {
                'crop': ['harvest', 'yield', 'produce', 'cultivation'],
                'soil': ['earth', 'ground', 'substrate', 'medium'],
                'irrigation': ['watering', 'moisture', 'hydrology'],
                'pest': ['insect', 'disease', 'pathogen', 'infestation'],
                'fertilizer': ['nutrient', 'amendment', 'enrichment']
            },
            'environmental': {
                'pollution': ['contamination', 'emission', 'waste', 'discharge'],
                'climate': ['weather', 'atmosphere', 'temperature', 'precipitation'],
                'conservation': ['protection', 'preservation', 'sustainability'],
                'ecosystem': ['environment', 'habitat', 'biodiversity', 'ecology']
            }
        }
    
    def _load_entity_patterns(self) -> Dict[str, str]:
        """Load patterns for entity extraction"""
        return {
            'location': r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\s+(?:County|State|Province|Region|Area)\b',
            'date': r'\b\d{4}(?:-\d{2})?(?:-\d{2})?\b',
            'measurement': r'\b\d+(?:\.\d+)?\s*(?:hectares|acres|tons|kg|m³|ft³)\b',
            'percentage': r'\b\d+(?:\.\d+)?%\b',
            'temperature': r'\b\d+(?:\.\d+)?\s*(?:°C|°F|Celsius|Fahrenheit)\b'
        }
    
    def _load_context_keywords(self) -> Dict[str, List[str]]:
        """Load context-indicating keywords"""
        return {
            'temporal': ['recent', 'historical', 'current', 'future', 'trend', 'change'],
            'spatial': ['location', 'region', 'area', 'site', 'zone', 'boundary'],
            'comparative': ['compare', 'versus', 'difference', 'similar', 'better', 'worse'],
            'causal': ['because', 'due to', 'result', 'effect', 'impact', 'cause'],
            'procedural': ['how to', 'steps', 'process', 'method', 'procedure', 'guide']
        }
    
    def understand_query(self, query: str) -> QueryUnderstanding:
        """
        Perform deep understanding of a query
        
        Args:
            query: The input query
            
        Returns:
            QueryUnderstanding object with extracted information
        """
        start_time = datetime.now()
        
        try:
            # Analyze query semantics
            analysis = self.semantic_analyzer.analyze_query(query)
            
            # Extract entities
            entities = self._extract_entities(query)
            
            # Extract relationships
            relationships = self._extract_relationships(query, entities)
            
            # Identify context clues
            context_clues = self._identify_context_clues(query)
            
            # Identify implicit requirements
            implicit_requirements = self._identify_implicit_requirements(query, analysis)
            
            # Extract domain-specific terms
            domain_terms = self._extract_domain_terms(query)
            
            # Extract temporal and spatial context
            temporal_context = self._extract_temporal_context(query)
            spatial_context = self._extract_spatial_context(query)
            
            # Calculate confidence
            confidence = self._calculate_understanding_confidence(
                entities, relationships, context_clues, domain_terms
            )
            
            understanding = QueryUnderstanding(
                query=query,
                entities=entities,
                relationships=relationships,
                context_clues=context_clues,
                implicit_requirements=implicit_requirements,
                domain_specific_terms=domain_terms,
                temporal_context=temporal_context,
                spatial_context=spatial_context,
                confidence=confidence
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            track_query_expansion_metrics('query_understanding', execution_time, 1)
            
            logger.info(f"Query understanding completed in {execution_time:.3f}s")
            logger.info(f"Extracted {len(entities)} entities, {len(relationships)} relationships")
            logger.info(f"Confidence: {confidence:.3f}")
            
            return understanding
            
        except Exception as e:
            logger.error(f"Error in query understanding: {e}")
            execution_time = (datetime.now() - start_time).total_seconds()
            track_query_expansion_metrics('query_understanding_error', execution_time, 0, error_message=str(e))
            raise
    
    def expand_query(self, query: str, strategy: str = 'comprehensive') -> QueryExpansion:
        """
        Expand a query using specified strategy
        
        Args:
            query: The input query
            strategy: Expansion strategy ('comprehensive', 'focused', 'semantic', 'domain')
            
        Returns:
            QueryExpansion object with expanded queries
        """
        start_time = datetime.now()
        
        try:
            # Understand the query first
            understanding = self.understand_query(query)
            
            # Generate expansions based on strategy
            if strategy == 'comprehensive':
                expanded_queries = self._comprehensive_expansion(query, understanding)
                expansion_type = 'comprehensive'
            elif strategy == 'focused':
                expanded_queries = self._focused_expansion(query, understanding)
                expansion_type = 'focused'
            elif strategy == 'semantic':
                expanded_queries = self._semantic_expansion(query, understanding)
                expansion_type = 'semantic'
            elif strategy == 'domain':
                expanded_queries = self._domain_expansion(query, understanding)
                expansion_type = 'domain'
            else:
                raise ValueError(f"Unknown expansion strategy: {strategy}")
            
            # Calculate confidence and reasoning
            confidence = self._calculate_expansion_confidence(expanded_queries, understanding)
            reasoning = self._generate_expansion_reasoning(expanded_queries, understanding)
            
            expansion = QueryExpansion(
                original_query=query,
                expanded_queries=expanded_queries,
                expansion_type=expansion_type,
                confidence=confidence,
                reasoning=reasoning,
                timestamp=datetime.now()
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            track_query_expansion_metrics('query_expansion', execution_time, len(expanded_queries))
            
            logger.info(f"Query expansion completed in {execution_time:.3f}s")
            logger.info(f"Generated {len(expanded_queries)} expanded queries")
            logger.info(f"Strategy: {strategy}, Confidence: {confidence:.3f}")
            
            return expansion
            
        except Exception as e:
            logger.error(f"Error in query expansion: {e}")
            execution_time = (datetime.now() - start_time).total_seconds()
            track_query_expansion_metrics('query_expansion_error', execution_time, 0, error_message=str(e))
            raise
    
    def generate_query_variations(self, query: str, target_strategies: List[str] = None) -> List[QueryVariation]:
        """
        Generate query variations for different search strategies
        
        Args:
            query: The input query
            target_strategies: List of target strategies ('semantic', 'keyword', 'hybrid', 'exact')
            
        Returns:
            List of QueryVariation objects
        """
        start_time = datetime.now()
        
        try:
            if target_strategies is None:
                target_strategies = ['semantic', 'keyword', 'hybrid', 'exact']
            
            understanding = self.understand_query(query)
            variations = []
            
            for strategy in target_strategies:
                if strategy == 'semantic':
                    variation = self._create_semantic_variation(query, understanding)
                elif strategy == 'keyword':
                    variation = self._create_keyword_variation(query, understanding)
                elif strategy == 'hybrid':
                    variation = self._create_hybrid_variation(query, understanding)
                elif strategy == 'exact':
                    variation = self._create_exact_variation(query, understanding)
                else:
                    continue
                
                variations.append(variation)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            track_query_expansion_metrics('query_variations', execution_time, len(variations))
            
            logger.info(f"Generated {len(variations)} query variations in {execution_time:.3f}s")
            
            return variations
            
        except Exception as e:
            logger.error(f"Error generating query variations: {e}")
            execution_time = (datetime.now() - start_time).total_seconds()
            track_query_expansion_metrics('query_variations_error', execution_time, 0, error_message=str(e))
            raise
    
    def _extract_entities(self, query: str) -> List[str]:
        """Extract entities from query using patterns and NLP"""
        entities = []
        
        # Extract using patterns
        for entity_type, pattern in self.entity_patterns.items():
            matches = re.findall(pattern, query, re.IGNORECASE)
            entities.extend(matches)
        
        # Extract domain-specific entities
        words = query.lower().split()
        for domain, synonyms in self.domain_synonyms.items():
            for term, related in synonyms.items():
                if term in words:
                    entities.append(term)
                    entities.extend(related[:2])  # Add top 2 related terms
        
        return list(set(entities))
    
    def _extract_relationships(self, query: str, entities: List[str]) -> List[Tuple[str, str, str]]:
        """Extract relationships between entities"""
        relationships = []
        
        # Simple relationship extraction based on proximity
        words = query.lower().split()
        
        for i, entity1 in enumerate(entities):
            for j, entity2 in enumerate(entities):
                if i != j and entity1 in words and entity2 in words:
                    # Find words between entities as potential relations
                    idx1 = words.index(entity1)
                    idx2 = words.index(entity2)
                    
                    if abs(idx1 - idx2) <= 3:  # Within 3 words
                        start = min(idx1, idx2) + 1
                        end = max(idx1, idx2)
                        relation_words = words[start:end]
                        
                        if relation_words:
                            relation = ' '.join(relation_words)
                            relationships.append((entity1, relation, entity2))
        
        return relationships
    
    def _identify_context_clues(self, query: str) -> List[str]:
        """Identify context-indicating keywords"""
        clues = []
        query_lower = query.lower()
        
        for context_type, keywords in self.context_keywords.items():
            for keyword in keywords:
                if keyword in query_lower:
                    clues.append(f"{context_type}:{keyword}")
        
        return clues
    
    def _identify_implicit_requirements(self, query: str, analysis: QueryAnalysis) -> List[str]:
        """Identify implicit requirements based on query analysis"""
        requirements = []
        
        # Based on intent
        if analysis.complexity.intent.value == 'factual':
            requirements.append('accurate_information')
        elif analysis.complexity.intent.value == 'analytical':
            requirements.append('detailed_analysis')
        elif analysis.complexity.intent.value == 'procedural':
            requirements.append('step_by_step_guidance')
        
        # Based on complexity
        if analysis.complexity.complexity_level == 'high':
            requirements.append('comprehensive_coverage')
        elif analysis.complexity.complexity_level == 'low':
            requirements.append('concise_summary')
        
        # Based on domain keywords
        if analysis.domain_keywords:
            requirements.append('domain_specific_knowledge')
        
        return requirements
    
    def _extract_domain_terms(self, query: str) -> List[str]:
        """Extract domain-specific terms"""
        terms = []
        query_lower = query.lower()
        
        for domain, synonyms in self.domain_synonyms.items():
            for term in synonyms:
                if term in query_lower:
                    terms.append(term)
        
        return terms
    
    def _extract_temporal_context(self, query: str) -> Optional[str]:
        """Extract temporal context from query"""
        temporal_patterns = [
            r'\b(recent|current|latest|new|old|historical|future)\b',
            r'\b\d{4}\b',  # Year
            r'\b(last|next|previous|upcoming)\s+(year|month|week|day)\b'
        ]
        
        for pattern in temporal_patterns:
            match = re.search(pattern, query, re.IGNORECASE)
            if match:
                return match.group(0)
        
        return None
    
    def _extract_spatial_context(self, query: str) -> Optional[str]:
        """Extract spatial context from query"""
        spatial_patterns = [
            r'\b(location|region|area|site|zone|boundary)\b',
            r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\s+(?:County|State|Province|Region)\b'
        ]
        
        for pattern in spatial_patterns:
            match = re.search(pattern, query, re.IGNORECASE)
            if match:
                return match.group(0)
        
        return None
    
    def _calculate_understanding_confidence(self, entities: List[str], relationships: List[Tuple],
                                          context_clues: List[str], domain_terms: List[str]) -> float:
        """Calculate confidence in query understanding"""
        confidence = 0.0
        
        # Entity confidence
        if entities:
            confidence += 0.3
        
        # Relationship confidence
        if relationships:
            confidence += 0.2
        
        # Context confidence
        if context_clues:
            confidence += 0.2
        
        # Domain confidence
        if domain_terms:
            confidence += 0.3
        
        return min(confidence, 1.0)
    
    def _comprehensive_expansion(self, query: str, understanding: QueryUnderstanding) -> List[str]:
        """Generate comprehensive query expansion"""
        expansions = [query]  # Include original
        
        # Synonym expansion
        expansions.extend(self._expand_synonyms(query, understanding))
        
        # Related terms expansion
        expansions.extend(self._expand_related_terms(query, understanding))
        
        # Contextual expansion
        expansions.extend(self._expand_contextual(query, understanding))
        
        # Domain expansion
        expansions.extend(self._expand_domain_specific(query, understanding))
        
        return list(set(expansions))[:10]  # Limit to 10 unique expansions
    
    def _focused_expansion(self, query: str, understanding: QueryUnderstanding) -> List[str]:
        """Generate focused query expansion"""
        expansions = [query]
        
        # Focus on most relevant entities
        if understanding.entities:
            main_entity = understanding.entities[0]
            expansions.append(f'"{main_entity}" {query}')
        
        # Focus on domain terms
        if understanding.domain_specific_terms:
            domain_term = understanding.domain_specific_terms[0]
            expansions.append(f'{domain_term} {query}')
        
        return list(set(expansions))[:5]
    
    def _semantic_expansion(self, query: str, understanding: QueryUnderstanding) -> List[str]:
        """Generate semantic query expansion"""
        expansions = [query]
        
        # Use semantic analyzer for expansion
        analysis = self.semantic_analyzer.analyze_query(query)
        
        # Add domain keywords
        if analysis.domain_keywords:
            for keyword in analysis.domain_keywords[:3]:
                expansions.append(f'{keyword} {query}')
        
        # Add intent-based expansion
        if analysis.complexity.intent.value == 'factual':
            expansions.append(f'information about {query}')
        elif analysis.complexity.intent.value == 'analytical':
            expansions.append(f'analysis of {query}')
        
        return list(set(expansions))[:5]
    
    def _domain_expansion(self, query: str, understanding: QueryUnderstanding) -> List[str]:
        """Generate domain-specific query expansion"""
        expansions = [query]
        
        # Add domain synonyms
        for domain, synonyms in self.domain_synonyms.items():
            for term, related in synonyms.items():
                if term in query.lower():
                    for related_term in related[:2]:
                        expanded = query.lower().replace(term, related_term)
                        expansions.append(expanded)
        
        return list(set(expansions))[:5]
    
    def _expand_synonyms(self, query: str, understanding: QueryUnderstanding) -> List[str]:
        """Expand query with synonyms"""
        expansions = []
        words = query.lower().split()
        
        for domain, synonyms in self.domain_synonyms.items():
            for term, related in synonyms.items():
                if term in words:
                    for synonym in related[:2]:
                        expanded = query.lower().replace(term, synonym)
                        expansions.append(expanded)
        
        return expansions
    
    def _expand_related_terms(self, query: str, understanding: QueryUnderstanding) -> List[str]:
        """Expand query with related terms"""
        expansions = []
        
        # Add related terms based on entities
        for entity in understanding.entities[:3]:
            expansions.append(f'{query} {entity}')
        
        return expansions
    
    def _expand_contextual(self, query: str, understanding: QueryUnderstanding) -> List[str]:
        """Expand query with contextual information"""
        expansions = []
        
        # Add temporal context
        if understanding.temporal_context:
            expansions.append(f'{query} {understanding.temporal_context}')
        
        # Add spatial context
        if understanding.spatial_context:
            expansions.append(f'{query} {understanding.spatial_context}')
        
        return expansions
    
    def _expand_domain_specific(self, query: str, understanding: QueryUnderstanding) -> List[str]:
        """Expand query with domain-specific terms"""
        expansions = []
        
        # Add domain terms
        for term in understanding.domain_specific_terms[:3]:
            expansions.append(f'{term} {query}')
        
        return expansions
    
    def _reformulate_query(self, query: str, understanding: QueryUnderstanding) -> List[str]:
        """Reformulate query for different search strategies"""
        reformulations = []
        
        # Question reformulation
        if '?' in query:
            reformulations.append(query.replace('?', ''))
        
        # Statement reformulation
        if not query.endswith('?'):
            reformulations.append(f'{query}?')
        
        # Keyword extraction
        keywords = ' '.join(understanding.entities[:3])
        if keywords:
            reformulations.append(keywords)
        
        return reformulations
    
    def _calculate_expansion_confidence(self, expanded_queries: List[str], 
                                      understanding: QueryUnderstanding) -> float:
        """Calculate confidence in query expansion"""
        if not expanded_queries:
            return 0.0
        
        # Base confidence on understanding confidence
        base_confidence = understanding.confidence
        
        # Adjust based on number of expansions
        expansion_factor = min(len(expanded_queries) / 5.0, 1.0)
        
        # Adjust based on diversity of expansions
        diversity_factor = len(set(expanded_queries)) / len(expanded_queries)
        
        return base_confidence * expansion_factor * diversity_factor
    
    def _generate_expansion_reasoning(self, expanded_queries: List[str], 
                                    understanding: QueryUnderstanding) -> str:
        """Generate reasoning for query expansion"""
        reasons = []
        
        if understanding.entities:
            reasons.append(f"Extracted {len(understanding.entities)} entities")
        
        if understanding.domain_specific_terms:
            reasons.append(f"Identified {len(understanding.domain_specific_terms)} domain terms")
        
        if understanding.context_clues:
            reasons.append(f"Found {len(understanding.context_clues)} context clues")
        
        if understanding.temporal_context:
            reasons.append(f"Temporal context: {understanding.temporal_context}")
        
        if understanding.spatial_context:
            reasons.append(f"Spatial context: {understanding.spatial_context}")
        
        return "; ".join(reasons) if reasons else "Basic expansion applied"
    
    def _create_semantic_variation(self, query: str, understanding: QueryUnderstanding) -> QueryVariation:
        """Create semantic search variation"""
        # Focus on meaning and concepts
        semantic_query = query
        if understanding.entities:
            semantic_query = f'"{understanding.entities[0]}" {query}'
        
        return QueryVariation(
            query=semantic_query,
            strategy='semantic',
            weight=0.8,
            reasoning='Semantic variation focusing on meaning and concepts',
            expected_documents=20
        )
    
    def _create_keyword_variation(self, query: str, understanding: QueryUnderstanding) -> QueryVariation:
        """Create keyword search variation"""
        # Focus on exact terms
        keywords = ' '.join(understanding.entities[:3])
        if not keywords:
            keywords = query
        
        return QueryVariation(
            query=keywords,
            strategy='keyword',
            weight=0.6,
            reasoning='Keyword variation focusing on exact term matching',
            expected_documents=15
        )
    
    def _create_hybrid_variation(self, query: str, understanding: QueryUnderstanding) -> QueryVariation:
        """Create hybrid search variation"""
        # Combine semantic and keyword approaches
        hybrid_query = query
        if understanding.domain_specific_terms:
            hybrid_query = f'{understanding.domain_specific_terms[0]} {query}'
        
        return QueryVariation(
            query=hybrid_query,
            strategy='hybrid',
            weight=0.9,
            reasoning='Hybrid variation combining semantic and keyword approaches',
            expected_documents=25
        )
    
    def _create_exact_variation(self, query: str, understanding: QueryUnderstanding) -> QueryVariation:
        """Create exact match variation"""
        # Use exact phrase matching
        exact_query = f'"{query}"'
        
        return QueryVariation(
            query=exact_query,
            strategy='exact',
            weight=0.4,
            reasoning='Exact variation for precise phrase matching',
            expected_documents=5
        )


# Global instance
query_expander = QueryExpander()


def understand_query(query: str) -> QueryUnderstanding:
    """Convenience function for query understanding"""
    return query_expander.understand_query(query)


def expand_query(query: str, strategy: str = 'comprehensive') -> QueryExpansion:
    """Convenience function for query expansion"""
    return query_expander.expand_query(query, strategy)


def generate_query_variations(query: str, target_strategies: List[str] = None) -> List[QueryVariation]:
    """Convenience function for generating query variations"""
    return query_expander.generate_query_variations(query, target_strategies) 